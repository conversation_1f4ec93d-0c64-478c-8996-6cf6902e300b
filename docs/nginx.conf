# Nginx配置示例 - 用于支持ThinkPHP路由
# 请将此配置添加到您的nginx站点配置中

server {
    listen 80;
    server_name news.test.jijiaox.com;
    root /www/wwwroot/news.test.jijiaox.com/public;
    index index.php index.html index.htm;

    # 处理静态文件
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri =404;
    }

    # 处理PHP文件和路由
    location / {
        try_files $uri $uri/ /index.php$is_args$args;
    }

    # 处理PHP
    location ~ \.php$ {
        fastcgi_pass 127.0.0.1:9000;  # 或者 unix:/var/run/php/php8.1-fpm.sock
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # 禁止访问隐藏文件
    location ~ /\. {
        deny all;
    }

    # 禁止访问敏感目录
    location ~ ^/(app|config|database|runtime|vendor)/ {
        deny all;
    }
}
