# API接口测试工具使用说明

## 🎯 工具概述

这是一套完整的API接口测试和自动修复工具，可以全面测试所有API接口并提供自动修复建议。

## 📁 文件结构

```
docs/test/
├── README.md                    # 使用说明（本文件）
├── ComprehensiveApiTester.php   # 核心测试类
├── AutoFixHelper.php           # 自动修复助手
├── run_api_tests.php           # 命令行测试脚本
├── one_click_test.sh           # 一键测试脚本
├── api_test_web.html           # Web测试界面
├── ArticleCategoryApiTest.php  # 分类API测试
├── portal_system_test.html     # 门户系统测试
└── article_system_test.php     # 文章系统测试
```

## 🚀 快速开始

### 方法一：一键测试（推荐）

```bash
# 进入项目根目录
cd /www/wwwroot/news.test.jijiaox.com

# 运行一键测试
./docs/test/one_click_test.sh

# 或指定自定义URL
./docs/test/one_click_test.sh http://your-domain.com
```

### 方法二：命令行测试

```bash
# 基础测试
php docs/test/run_api_tests.php

# 指定URL
php docs/test/run_api_tests.php http://news.test.jijiaox.com

# 启用自动修复
php docs/test/run_api_tests.php http://news.test.jijiaox.com --auto-fix
```

### 方法三：Web界面测试

1. 在浏览器中打开：`http://your-domain.com/docs/test/api_test_web.html`
2. 输入API基础地址
3. 选择是否启用自动修复
4. 点击"开始测试"

## 🧪 测试覆盖范围

### 认证模块
- ✅ 登录状态检查
- ✅ 用户登录
- ✅ 用户登出
- ✅ 获取用户信息

### 用户管理
- ✅ 获取用户列表
- ✅ 创建用户
- ✅ 获取用户详情
- ✅ 更新用户
- ✅ 删除用户
- ✅ 修改密码

### 分类管理
- ✅ 获取分类列表
- ✅ 获取分类树
- ✅ 创建分类
- ✅ 更新分类
- ✅ 删除分类
- ✅ 切换显示状态

### 文章管理
- ✅ 获取文章列表
- ✅ 获取文章统计
- ✅ 创建文章
- ✅ 更新文章
- ✅ 删除文章
- ✅ 切换置顶状态

### 自定义字段
- ✅ 获取字段列表
- ✅ 创建字段
- ✅ 更新字段
- ✅ 删除字段
- ✅ 切换启用状态

### 门户配置
- ✅ 获取配置列表
- ✅ 创建配置
- ✅ 批量设置配置
- ✅ 获取配置分组

### 门户模块
- ✅ 获取模块列表
- ✅ 创建模块
- ✅ 切换模块状态
- ✅ 获取启用模块

### 公开接口
- ✅ 公开分类接口
- ✅ 公开门户模块

## 🔧 自动修复功能

### 支持的修复项目

1. **连接问题**
   - 创建.htaccess文件
   - 检查入口文件
   - 设置URL重写规则

2. **认证问题**
   - 生成默认管理员账号SQL
   - 检查密码加密方式

3. **权限问题**
   - 更新中间件配置
   - 修复排除路由

4. **路由问题**
   - 检查路由配置文件
   - 验证控制器引用

5. **数据库问题**
   - 生成数据库检查脚本
   - 检查表结构

6. **通用修复**
   - 创建必要目录
   - 设置目录权限

### 手动执行自动修复

```bash
# 基于测试报告执行修复
php docs/test/AutoFixHelper.php docs/test/api_test_report.json
```

## 📊 测试报告

### 报告文件

- `api_test_report.json` - 详细测试报告
- `auto_fix_report.json` - 自动修复报告
- `final_test_report.md` - 最终测试总结

### 报告内容

```json
{
  "test_summary": {
    "total_tests": 50,
    "passed_tests": 45,
    "failed_tests": 5,
    "success_rate": 90.0,
    "test_time": "2025-01-11 15:30:00",
    "base_url": "http://news.test.jijiaox.com"
  },
  "test_results": [...],
  "created_resources": {...},
  "recommendations": [...]
}
```

## 🛠️ 故障排除

### 常见问题

1. **连接失败**
   ```
   解决方案：
   - 检查Web服务器是否运行
   - 确认URL是否正确
   - 检查防火墙设置
   ```

2. **登录失败**
   ```
   解决方案：
   - 运行SQL创建默认管理员：docs/sql/create_admin.sql
   - 检查数据库连接
   - 确认用户表结构
   ```

3. **权限错误**
   ```
   解决方案：
   - 检查中间件配置
   - 确认路由权限设置
   - 验证Token生成逻辑
   ```

4. **404错误**
   ```
   解决方案：
   - 检查URL重写规则
   - 确认路由配置
   - 验证控制器文件存在
   ```

### 调试模式

```bash
# 启用详细输出
php docs/test/run_api_tests.php http://localhost --verbose

# 查看具体错误
tail -f runtime/log/error.log
```

## 📈 性能基准

### 成功率标准

- **90%以上** - 优秀，系统运行良好
- **70-89%** - 良好，建议优化失败项目
- **70%以下** - 需要关注，优先修复问题

### 响应时间标准

- **< 200ms** - 优秀
- **200-500ms** - 良好
- **500ms-1s** - 可接受
- **> 1s** - 需要优化

## 🔄 持续集成

### 集成到CI/CD

```yaml
# .github/workflows/api-test.yml
name: API Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.1'
      - name: Run API Tests
        run: |
          php docs/test/run_api_tests.php http://localhost
```

### 定时测试

```bash
# 添加到crontab
# 每小时执行一次API测试
0 * * * * cd /www/wwwroot/news.test.jijiaox.com && php docs/test/run_api_tests.php http://localhost > /tmp/api_test.log 2>&1
```

## 📞 技术支持

如果遇到问题，请：

1. 查看测试报告中的详细错误信息
2. 运行自动修复工具
3. 检查系统日志文件
4. 参考故障排除指南

---

**工具版本**: 1.0.0  
**最后更新**: 2025年1月11日  
**开发者**: Claude 4.0 sonnet 🐾
