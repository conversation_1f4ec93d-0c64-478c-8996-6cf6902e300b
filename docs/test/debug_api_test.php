<?php
/**
 * 调试API测试工具
 * 
 * 专门测试修复后的API端点
 */

echo "🔧 调试API测试工具\n";
echo "==================\n\n";

// 测试配置
$baseUrl = 'http://news.test.jijiaox.com';
$apiPrefix = '/index.php/api';

// 调试测试用例
$debugTestCases = [
    // 基础测试
    [
        'name' => 'API测试端点',
        'url' => $baseUrl . $apiPrefix . '/test',
        'method' => 'GET'
    ],
    [
        'name' => 'API健康检查',
        'url' => $baseUrl . $apiPrefix . '/health',
        'method' => 'GET'
    ],
    
    // 文章相关测试
    [
        'name' => '文章列表(简化版)',
        'url' => $baseUrl . $apiPrefix . '/articles/',
        'method' => 'GET'
    ],
    [
        'name' => '热门文章',
        'url' => $baseUrl . $apiPrefix . '/articles/hot',
        'method' => 'GET'
    ],
    [
        'name' => '最新文章',
        'url' => $baseUrl . $apiPrefix . '/articles/latest',
        'method' => 'GET'
    ],
    [
        'name' => '轮播文章',
        'url' => $baseUrl . $apiPrefix . '/articles/banner',
        'method' => 'GET'
    ],
    
    // 分类测试
    [
        'name' => '分类列表',
        'url' => $baseUrl . $apiPrefix . '/categories/',
        'method' => 'GET'
    ],
    
    // 门户测试
    [
        'name' => '启用的门户模块',
        'url' => $baseUrl . $apiPrefix . '/public-portal/modules/enabled',
        'method' => 'GET'
    ]
];

// 调试测试函数
function runDebugTest($testCase) {
    echo "🧪 调试测试: " . $testCase['name'] . "\n";
    echo "   URL: " . $testCase['url'] . "\n";
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $testCase['url'],
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 15,
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            'Accept: application/json'
        ],
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_FOLLOWLOCATION => false,
        CURLOPT_VERBOSE => false
    ]);
    
    $startTime = microtime(true);
    $response = curl_exec($ch);
    $endTime = microtime(true);
    
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
    $error = curl_error($ch);
    curl_close($ch);
    
    $responseTime = round(($endTime - $startTime) * 1000, 2);
    
    echo "   响应时间: {$responseTime}ms\n";
    echo "   HTTP状态码: $httpCode\n";
    echo "   内容类型: " . ($contentType ?: '未知') . "\n";
    
    // 检查连接错误
    if ($error) {
        echo "   ❌ 连接错误: $error\n\n";
        return false;
    }
    
    // 检查HTTP状态码
    if ($httpCode !== 200) {
        echo "   ❌ HTTP错误: $httpCode\n";
        if ($response) {
            echo "   错误响应: " . substr($response, 0, 200) . "...\n";
        }
        echo "\n";
        return false;
    }
    
    // 检查响应内容
    if (empty($response)) {
        echo "   ❌ 空响应\n\n";
        return false;
    }
    
    // 尝试解析JSON
    $jsonData = json_decode($response, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        echo "   ❌ JSON解析失败: " . json_last_error_msg() . "\n";
        echo "   响应内容: " . substr($response, 0, 300) . "...\n\n";
        return false;
    }
    
    // 检查响应格式
    if (!isset($jsonData['status'])) {
        echo "   ❌ 响应格式错误: 缺少status字段\n";
        echo "   响应内容: " . json_encode($jsonData, JSON_UNESCAPED_UNICODE) . "\n\n";
        return false;
    }
    
    $status = $jsonData['status'];
    $message = $jsonData['message'] ?? '无消息';
    
    if ($status === 'success' || $status === 'healthy') {
        echo "   ✅ 测试通过\n";
        echo "   状态: $status\n";
        echo "   消息: $message\n";
        
        // 显示数据摘要
        if (isset($jsonData['data'])) {
            $data = $jsonData['data'];
            if (is_array($data)) {
                if (isset($data['total'])) {
                    echo "   数据总数: " . $data['total'] . "\n";
                }
                if (isset($data['articles']) && is_array($data['articles'])) {
                    echo "   文章数量: " . count($data['articles']) . "\n";
                }
                if (isset($data['categories']) && is_array($data['categories'])) {
                    echo "   分类数量: " . count($data['categories']) . "\n";
                }
                if (isset($data['modules']) && is_array($data['modules'])) {
                    echo "   模块数量: " . count($data['modules']) . "\n";
                }
            }
        }
        echo "\n";
        return true;
    } else {
        echo "   ❌ 业务错误\n";
        echo "   状态: $status\n";
        echo "   消息: $message\n";
        if (isset($jsonData['error'])) {
            echo "   错误详情: " . $jsonData['error'] . "\n";
        }
        echo "\n";
        return false;
    }
}

// 执行调试测试
echo "🎯 开始调试测试...\n\n";

$results = [];
$totalTests = count($debugTestCases);
$passedTests = 0;

foreach ($debugTestCases as $testCase) {
    $success = runDebugTest($testCase);
    $results[] = [
        'name' => $testCase['name'],
        'url' => $testCase['url'],
        'success' => $success
    ];
    
    if ($success) {
        $passedTests++;
    }
    
    // 短暂延迟避免请求过快
    usleep(200000); // 0.2秒
}

// 生成调试报告
echo "📊 调试测试结果\n";
echo "================\n";
echo "总测试数: $totalTests\n";
echo "通过数: $passedTests\n";
echo "失败数: " . ($totalTests - $passedTests) . "\n";
echo "通过率: " . round(($passedTests / $totalTests) * 100, 2) . "%\n\n";

echo "📋 详细结果\n";
echo "============\n";
foreach ($results as $result) {
    $status = $result['success'] ? '✅ 通过' : '❌ 失败';
    echo "- {$result['name']}: $status\n";
}

// 保存调试报告
$report = [
    'debug_time' => date('Y-m-d H:i:s'),
    'summary' => [
        'total_tests' => $totalTests,
        'passed_tests' => $passedTests,
        'failed_tests' => $totalTests - $passedTests,
        'pass_rate' => round(($passedTests / $totalTests) * 100, 2)
    ],
    'results' => $results
];

file_put_contents('debug_api_test_report.json', json_encode($report, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

echo "\n📄 调试报告已保存到: debug_api_test_report.json\n";

if ($passedTests === $totalTests) {
    echo "\n🎉 所有调试测试通过！API修复成功！\n";
} elseif ($passedTests > $totalTests / 2) {
    echo "\n✅ 大部分测试通过，API基本正常！\n";
} else {
    echo "\n⚠️ 多数测试失败，需要进一步调试。\n";
}

echo "\n✅ 调试测试完成！\n";
