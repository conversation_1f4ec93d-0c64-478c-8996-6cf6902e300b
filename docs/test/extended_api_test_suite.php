<?php
/**
 * 扩展API测试套件
 * 
 * 测试所有新增的API端点
 */

echo "🚀 扩展API测试套件\n";
echo "==================\n\n";

// 测试配置
$baseUrl = 'http://news.test.jijiaox.com';
$apiPrefix = '/index.php/api';

// 扩展测试用例
$extendedTestCases = [
    // 分类管理API测试
    [
        'name' => '获取分类列表',
        'url' => $baseUrl . $apiPrefix . '/categories/',
        'method' => 'GET',
        'expected_status' => 'success',
        'expected_fields' => ['status', 'message', 'data']
    ],
    
    // 文章管理API测试
    [
        'name' => '获取文章列表',
        'url' => $baseUrl . $apiPrefix . '/articles/',
        'method' => 'GET',
        'expected_status' => 'success',
        'expected_fields' => ['status', 'message', 'data']
    ],
    [
        'name' => '获取文章列表(带分页)',
        'url' => $baseUrl . $apiPrefix . '/articles/?page=1&limit=5',
        'method' => 'GET',
        'expected_status' => 'success',
        'expected_fields' => ['status', 'message', 'data']
    ],
    
    // 公开门户API测试
    [
        'name' => '获取启用的门户模块',
        'url' => $baseUrl . $apiPrefix . '/public-portal/modules/enabled',
        'method' => 'GET',
        'expected_status' => 'success',
        'expected_fields' => ['status', 'message', 'data']
    ],
    [
        'name' => '获取站点配置',
        'url' => $baseUrl . $apiPrefix . '/public-portal/configs/site',
        'method' => 'GET',
        'expected_status' => 'success',
        'expected_fields' => ['status', 'message', 'data']
    ],
    [
        'name' => '获取首页数据',
        'url' => $baseUrl . $apiPrefix . '/public-portal/home-data',
        'method' => 'GET',
        'expected_status' => 'success',
        'expected_fields' => ['status', 'message', 'data']
    ]
];

// 测试函数
function runExtendedApiTest($testCase) {
    echo "🧪 测试: " . $testCase['name'] . "\n";
    echo "   URL: " . $testCase['url'] . "\n";
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $testCase['url'],
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 15,
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            'Accept: application/json'
        ],
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_FOLLOWLOCATION => false
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    $result = [
        'test_name' => $testCase['name'],
        'url' => $testCase['url'],
        'http_code' => $httpCode,
        'success' => false,
        'errors' => [],
        'response_data' => null
    ];
    
    // 检查连接错误
    if ($error) {
        $result['errors'][] = "连接错误: $error";
        echo "   ❌ 连接失败: $error\n\n";
        return $result;
    }
    
    // 检查HTTP状态码
    if ($httpCode !== 200) {
        $result['errors'][] = "HTTP状态码错误: $httpCode";
        echo "   ❌ HTTP状态码: $httpCode\n";
        
        // 显示错误响应内容
        if ($response) {
            echo "   响应内容: " . substr($response, 0, 200) . "...\n";
        }
        echo "\n";
        return $result;
    }
    
    // 解析JSON响应
    $jsonData = json_decode($response, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        $result['errors'][] = "JSON解析失败: " . json_last_error_msg();
        echo "   ❌ JSON解析失败\n";
        echo "   响应内容: " . substr($response, 0, 200) . "...\n\n";
        return $result;
    }
    
    $result['response_data'] = $jsonData;
    
    // 检查预期状态
    if (isset($testCase['expected_status'])) {
        $actualStatus = $jsonData['status'] ?? null;
        if ($actualStatus !== $testCase['expected_status']) {
            $result['errors'][] = "状态不匹配: 期望 '{$testCase['expected_status']}', 实际 '$actualStatus'";
        }
    }
    
    // 检查预期字段
    if (isset($testCase['expected_fields'])) {
        foreach ($testCase['expected_fields'] as $field) {
            if (!isset($jsonData[$field])) {
                $result['errors'][] = "缺少字段: $field";
            }
        }
    }
    
    // 判断测试结果
    if (empty($result['errors'])) {
        $result['success'] = true;
        echo "   ✅ 测试通过\n";
        echo "   状态: " . ($jsonData['status'] ?? '未知') . "\n";
        echo "   消息: " . ($jsonData['message'] ?? '无消息') . "\n";
        
        // 显示数据摘要
        if (isset($jsonData['data'])) {
            $data = $jsonData['data'];
            if (is_array($data)) {
                if (isset($data['total'])) {
                    echo "   数据总数: " . $data['total'] . "\n";
                }
                if (isset($data['categories'])) {
                    echo "   分类数量: " . count($data['categories']) . "\n";
                }
                if (isset($data['articles'])) {
                    echo "   文章数量: " . count($data['articles']) . "\n";
                }
                if (isset($data['modules'])) {
                    echo "   模块数量: " . count($data['modules']) . "\n";
                }
                if (isset($data['configs'])) {
                    echo "   配置数量: " . count($data['configs']) . "\n";
                }
            }
        }
    } else {
        echo "   ❌ 测试失败\n";
        foreach ($result['errors'] as $error) {
            echo "   错误: $error\n";
        }
    }
    
    echo "\n";
    return $result;
}

// 执行扩展测试
echo "🎯 开始执行扩展API测试...\n\n";

$results = [];
$totalTests = count($extendedTestCases);
$passedTests = 0;

foreach ($extendedTestCases as $testCase) {
    $result = runExtendedApiTest($testCase);
    $results[] = $result;
    
    if ($result['success']) {
        $passedTests++;
    }
}

// 生成测试报告
echo "📊 扩展测试结果统计\n";
echo "====================\n";
echo "总测试数: $totalTests\n";
echo "通过数: $passedTests\n";
echo "失败数: " . ($totalTests - $passedTests) . "\n";
echo "通过率: " . round(($passedTests / $totalTests) * 100, 2) . "%\n\n";

// 详细结果
echo "📋 详细测试结果\n";
echo "================\n";

foreach ($results as $result) {
    $status = $result['success'] ? '✅ 通过' : '❌ 失败';
    echo "- {$result['test_name']}: $status\n";
    
    if (!$result['success']) {
        foreach ($result['errors'] as $error) {
            echo "  错误: $error\n";
        }
    }
}

// 保存扩展测试报告
$report = [
    'test_time' => date('Y-m-d H:i:s'),
    'test_type' => 'extended_api_test',
    'summary' => [
        'total_tests' => $totalTests,
        'passed_tests' => $passedTests,
        'failed_tests' => $totalTests - $passedTests,
        'pass_rate' => round(($passedTests / $totalTests) * 100, 2)
    ],
    'detailed_results' => $results
];

file_put_contents('extended_api_test_report.json', json_encode($report, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

echo "\n📄 扩展测试报告已保存到: extended_api_test_report.json\n";

// 最终状态
if ($passedTests === $totalTests) {
    echo "\n🎉 所有扩展API测试通过！系统功能完整！\n";
    echo "✅ 分类管理API正常\n";
    echo "✅ 文章管理API正常\n";
    echo "✅ 公开门户API正常\n";
    echo "✅ 数据库连接正常\n";
} else {
    echo "\n⚠️ 部分扩展测试失败，需要进一步调试\n";
    echo "这可能是由于数据库中没有数据或服务依赖问题\n";
}

echo "\n✅ 扩展API测试套件执行完成！\n";
