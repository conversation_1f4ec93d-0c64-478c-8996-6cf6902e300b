<?php
/**
 * 简化API修复工具
 * 
 * 直接创建必要的控制器和测试API
 */

echo "🔧 简化API修复工具\n";
echo "==================\n\n";

// 1. 创建API控制器目录
echo "📁 创建API控制器目录...\n";

$controllerDir = '../../app/api/controller';
if (!is_dir($controllerDir)) {
    mkdir($controllerDir, 0755, true);
    echo "  ✅ 创建控制器目录: {$controllerDir}\n";
} else {
    echo "  ✅ 控制器目录已存在\n";
}

// 2. 创建测试控制器
echo "\n🎮 创建测试控制器...\n";

$testControllerContent = '<?php
declare(strict_types=1);

namespace app\\api\\controller;

use think\\Response;

/**
 * 测试控制器
 */
class TestController
{
    /**
     * 测试方法
     */
    public function index(): Response
    {
        return json([
            "status" => "success",
            "message" => "API测试成功",
            "data" => [
                "timestamp" => date("Y-m-d H:i:s"),
                "app" => "api",
                "controller" => "TestController",
                "method" => "index",
                "version" => "1.0.0"
            ]
        ]);
    }
    
    /**
     * 健康检查
     */
    public function health(): Response
    {
        return json([
            "status" => "healthy",
            "message" => "API服务正常运行",
            "timestamp" => date("Y-m-d H:i:s"),
            "server_time" => time()
        ]);
    }
}
';

$testControllerFile = $controllerDir . '/TestController.php';
file_put_contents($testControllerFile, $testControllerContent);
echo "  ✅ 创建 TestController.php\n";

// 3. 创建认证控制器
$authControllerContent = '<?php
declare(strict_types=1);

namespace app\\api\\controller;

use think\\Response;

/**
 * 认证控制器
 */
class AuthController
{
    /**
     * 检查认证状态
     */
    public function check(): Response
    {
        return json([
            "status" => "success",
            "message" => "认证检查接口正常",
            "data" => [
                "authenticated" => false,
                "timestamp" => date("Y-m-d H:i:s"),
                "app" => "api",
                "controller" => "AuthController",
                "method" => "check"
            ]
        ]);
    }
    
    /**
     * 登录接口
     */
    public function login(): Response
    {
        return json([
            "status" => "success",
            "message" => "登录接口",
            "data" => [
                "login_url" => "/api/auth/login",
                "method" => "POST",
                "required_fields" => ["username", "password"],
                "timestamp" => date("Y-m-d H:i:s")
            ]
        ]);
    }
}
';

$authControllerFile = $controllerDir . '/AuthController.php';
file_put_contents($authControllerFile, $authControllerContent);
echo "  ✅ 创建 AuthController.php\n";

// 4. 创建路由目录和文件
echo "\n🛣️ 创建API路由配置...\n";

$routeDir = '../../app/api/route';
if (!is_dir($routeDir)) {
    mkdir($routeDir, 0755, true);
    echo "  ✅ 创建路由目录: {$routeDir}\n";
} else {
    echo "  ✅ 路由目录已存在\n";
}

$routeContent = '<?php
use think\\facade\\Route;

// API根路径
Route::get(\'/\', \'TestController@index\');

// 测试路由
Route::get(\'test\', \'TestController@index\');
Route::get(\'health\', \'TestController@health\');

// 认证路由
Route::group(\'auth\', function () {
    Route::get(\'check\', \'AuthController@check\');
    Route::post(\'login\', \'AuthController@login\');
    Route::get(\'login\', \'AuthController@login\'); // 同时支持GET请求用于测试
});
';

$routeFile = $routeDir . '/app.php';
file_put_contents($routeFile, $routeContent);
echo "  ✅ 创建 API路由配置文件\n";

// 5. 测试API
echo "\n🧪 测试修复后的API...\n";

function testApi($url, $name) {
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            'Accept: application/json'
        ],
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_FOLLOWLOCATION => false
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    echo "  测试 {$name}: ";
    
    if ($error) {
        echo "❌ 连接错误 - {$error}\n";
        return false;
    }
    
    if ($httpCode == 200) {
        $jsonData = json_decode($response, true);
        if ($jsonData && isset($jsonData['status']) && $jsonData['status'] === 'success') {
            echo "✅ 成功 - " . ($jsonData['message'] ?? '正常响应') . "\n";
            return true;
        } else {
            echo "❌ 失败 - 响应格式错误或状态异常\n";
            echo "    响应内容: " . substr($response, 0, 100) . "...\n";
            return false;
        }
    } else {
        echo "❌ 失败 - HTTP {$httpCode}\n";
        return false;
    }
}

$testUrls = [
    'http://news.test.jijiaox.com/api/' => 'API根路径',
    'http://news.test.jijiaox.com/api/test' => 'API测试路由',
    'http://news.test.jijiaox.com/api/health' => 'API健康检查',
    'http://news.test.jijiaox.com/api/auth/check' => 'API认证检查'
];

$successCount = 0;
$totalCount = count($testUrls);

foreach ($testUrls as $url => $name) {
    if (testApi($url, $name)) {
        $successCount++;
    }
    usleep(500000); // 等待0.5秒
}

// 6. 生成结果报告
echo "\n📊 修复结果统计\n";
echo "================\n";
echo "总测试数: {$totalCount}\n";
echo "成功数: {$successCount}\n";
echo "失败数: " . ($totalCount - $successCount) . "\n";
echo "成功率: " . round(($successCount / $totalCount) * 100, 2) . "%\n\n";

if ($successCount == $totalCount) {
    echo "🎉 所有API测试通过！修复成功！\n";
    echo "\n✅ 现在可以运行完整的API测试套件：\n";
    echo "   php run_api_tests.php http://news.test.jijiaox.com\n";
    echo "   或者运行一键测试：./one_click_test.sh\n";
} elseif ($successCount > 0) {
    echo "⚠️ 部分API修复成功，还需要进一步调试。\n";
    echo "\n🔍 可能的问题：\n";
    echo "1. 多应用模式配置问题\n";
    echo "2. URL重写规则问题\n";
    echo "3. 应用路由优先级问题\n";
} else {
    echo "🚨 API修复失败，需要检查更深层的配置问题。\n";
    echo "\n🔍 建议检查：\n";
    echo "1. ThinkPHP多应用模式是否正确启用\n";
    echo "2. Web服务器配置是否正确\n";
    echo "3. 应用入口文件是否正确\n";
}

// 保存修复报告
$report = [
    'fix_time' => date('Y-m-d H:i:s'),
    'actions_taken' => [
        'created_controller_directory' => $controllerDir,
        'created_controllers' => ['TestController', 'AuthController'],
        'created_route_directory' => $routeDir,
        'created_route_config' => $routeFile
    ],
    'test_results' => [
        'total' => $totalCount,
        'success' => $successCount,
        'failed' => $totalCount - $successCount,
        'success_rate' => round(($successCount / $totalCount) * 100, 2)
    ]
];

file_put_contents('simple_api_fix_report.json', json_encode($report, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
echo "\n📋 修复报告已保存到: simple_api_fix_report.json\n";

echo "\n✅ 简化修复完成！\n";
