{"test_time": "2025-07-11 14:05:16", "summary": {"total_tests": 4, "passed_tests": 4, "failed_tests": 0, "pass_rate": 100}, "performance": {"average_response_time_ms": 52.74, "min_response_time_ms": 49.99, "max_response_time_ms": 62.04}, "detailed_results": [{"test_name": "API测试端点", "url": "http://news.test.jijiaox.com/index.php/api/test", "http_code": 200, "success": true, "errors": [], "response_data": {"status": "success", "message": "API测试成功！🎉", "data": {"timestamp": "2025-07-11 14:05:16", "app": "api", "controller": "TestController", "method": "index", "version": "1.0.0", "server_info": {"php_version": "8.2.16", "thinkphp_version": "6.x", "multi_app_enabled": true}}}}, {"test_name": "API健康检查", "url": "http://news.test.jijiaox.com/index.php/api/health", "http_code": 200, "success": true, "errors": [], "response_data": {"status": "healthy", "message": "API服务正常运行", "timestamp": "2025-07-11 14:05:16", "server_time": **********, "uptime": "正常", "database": "连接正常", "cache": "正常"}}, {"test_name": "认证检查端点", "url": "http://news.test.jijiaox.com/index.php/api/auth/check", "http_code": 200, "success": true, "errors": [], "response_data": {"status": "success", "message": "认证检查接口正常", "data": {"authenticated": false, "timestamp": "2025-07-11 14:05:16", "app": "api", "controller": "AuthController", "method": "check"}}}, {"test_name": "登录端点信息", "url": "http://news.test.jijiaox.com/index.php/api/auth/login", "http_code": 200, "success": true, "errors": [], "response_data": {"status": "success", "message": "登录接口", "data": {"login_url": "/api/auth/login", "method": "POST", "required_fields": ["username", "password"], "timestamp": "2025-07-11 14:05:16"}}}]}