#!/bin/bash

# 一键API测试和修复脚本
# 使用方法: ./one_click_test.sh [base_url]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认配置
BASE_URL=${1:-"http://news.test.jijiaox.com"}
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

echo -e "${BLUE}🚀 一键API测试和修复工具${NC}"
echo -e "${BLUE}================================${NC}"
echo -e "目标地址: ${GREEN}$BASE_URL${NC}"
echo -e "项目根目录: ${GREEN}$PROJECT_ROOT${NC}"
echo ""

# 检查PHP环境
echo -e "${YELLOW}📋 检查运行环境...${NC}"
if ! command -v php &> /dev/null; then
    echo -e "${RED}❌ PHP未安装或不在PATH中${NC}"
    exit 1
fi

PHP_VERSION=$(php -r "echo PHP_VERSION;")
echo -e "✅ PHP版本: ${GREEN}$PHP_VERSION${NC}"

# 检查必要文件
echo -e "${YELLOW}📁 检查必要文件...${NC}"
REQUIRED_FILES=(
    "$SCRIPT_DIR/ComprehensiveApiTester.php"
    "$SCRIPT_DIR/AutoFixHelper.php"
    "$PROJECT_ROOT/app/api/route/app.php"
)

for file in "${REQUIRED_FILES[@]}"; do
    if [[ -f "$file" ]]; then
        echo -e "✅ $(basename "$file")"
    else
        echo -e "${RED}❌ 文件不存在: $file${NC}"
        exit 1
    fi
done

# 创建必要目录
echo -e "${YELLOW}📂 创建必要目录...${NC}"
mkdir -p "$PROJECT_ROOT/runtime/cache"
mkdir -p "$PROJECT_ROOT/runtime/log"
mkdir -p "$PROJECT_ROOT/docs/sql"
mkdir -p "$PROJECT_ROOT/docs/test"
echo -e "✅ 目录创建完成"

# 设置权限
echo -e "${YELLOW}🔐 设置目录权限...${NC}"
chmod -R 755 "$PROJECT_ROOT/runtime" 2>/dev/null || true
chmod -R 755 "$PROJECT_ROOT/docs/test" 2>/dev/null || true
echo -e "✅ 权限设置完成"

# 第一次测试
echo -e "${YELLOW}🧪 执行第一次API测试...${NC}"
cd "$SCRIPT_DIR"
php run_api_tests.php "$BASE_URL" > test_output.log 2>&1

# 检查测试结果
if [[ -f "api_test_report.json" ]]; then
    echo -e "✅ 测试报告生成成功"
    
    # 解析测试结果
    SUCCESS_RATE=$(php -r "
        \$report = json_decode(file_get_contents('api_test_report.json'), true);
        echo \$report['test_summary']['success_rate'] ?? 0;
    ")
    
    echo -e "📊 第一次测试成功率: ${GREEN}$SUCCESS_RATE%${NC}"
    
    # 如果成功率低于80%，执行自动修复
    if (( $(echo "$SUCCESS_RATE < 80" | bc -l) )); then
        echo -e "${YELLOW}🔧 成功率较低，执行自动修复...${NC}"
        php AutoFixHelper.php api_test_report.json
        
        # 等待一下让修复生效
        sleep 2
        
        # 第二次测试
        echo -e "${YELLOW}🧪 执行第二次API测试...${NC}"
        php run_api_tests.php "$BASE_URL" > test_output_2.log 2>&1
        
        if [[ -f "api_test_report.json" ]]; then
            SUCCESS_RATE_2=$(php -r "
                \$report = json_decode(file_get_contents('api_test_report.json'), true);
                echo \$report['test_summary']['success_rate'] ?? 0;
            ")
            
            echo -e "📊 第二次测试成功率: ${GREEN}$SUCCESS_RATE_2%${NC}"
            
            # 比较改善情况
            IMPROVEMENT=$(php -r "echo $SUCCESS_RATE_2 - $SUCCESS_RATE;")
            if (( $(echo "$IMPROVEMENT > 0" | bc -l) )); then
                echo -e "📈 成功率提升了: ${GREEN}+$IMPROVEMENT%${NC}"
            fi
        fi
    else
        echo -e "${GREEN}🎉 测试成功率良好，无需自动修复${NC}"
    fi
else
    echo -e "${RED}❌ 测试报告生成失败${NC}"
    cat test_output.log
    exit 1
fi

# 生成最终报告
echo -e "${YELLOW}📋 生成最终报告...${NC}"

FINAL_REPORT="final_test_report.md"
cat > "$FINAL_REPORT" << EOF
# API测试最终报告

## 测试信息
- 测试时间: $(date '+%Y-%m-%d %H:%M:%S')
- 目标地址: $BASE_URL
- 测试工具版本: 1.0.0

## 测试结果
EOF

if [[ -f "api_test_report.json" ]]; then
    php -r "
        \$report = json_decode(file_get_contents('api_test_report.json'), true);
        \$summary = \$report['test_summary'];
        echo \"- 总测试数: {\$summary['total_tests']}\n\";
        echo \"- 通过测试: {\$summary['passed_tests']}\n\";
        echo \"- 失败测试: {\$summary['failed_tests']}\n\";
        echo \"- 成功率: {\$summary['success_rate']}%\n\n\";
        
        if (!empty(\$report['recommendations'])) {
            echo \"## 修复建议\n\n\";
            foreach (\$report['recommendations'] as \$rec) {
                echo \"- \$rec\n\";
            }
        }
    " >> "$FINAL_REPORT"
fi

cat >> "$FINAL_REPORT" << EOF

## 相关文件
- 详细测试报告: api_test_report.json
- 自动修复报告: auto_fix_report.json
- 测试日志: test_output.log
- Web测试界面: api_test_web.html

## 后续步骤
1. 查看详细测试报告了解具体问题
2. 根据修复建议手动处理剩余问题
3. 重新运行测试验证修复效果
4. 部署到生产环境前再次测试
EOF

echo -e "✅ 最终报告已生成: ${GREEN}$FINAL_REPORT${NC}"

# 显示总结
echo ""
echo -e "${BLUE}🎯 测试完成总结${NC}"
echo -e "${BLUE}==================${NC}"

if [[ -f "api_test_report.json" ]]; then
    php -r "
        \$report = json_decode(file_get_contents('api_test_report.json'), true);
        \$rate = \$report['test_summary']['success_rate'];
        
        if (\$rate >= 90) {
            echo \"🎉 优秀！API测试通过率达到 \$rate%\n\";
            echo \"系统运行状态良好，可以正常使用。\n\";
        } elseif (\$rate >= 70) {
            echo \"⚠️  良好！API测试通过率为 \$rate%\n\";
            echo \"建议查看失败的测试项目并进行优化。\n\";
        } else {
            echo \"🚨 需要关注！API测试通过率仅为 \$rate%\n\";
            echo \"请优先修复失败的测试项目。\n\";
        }
    "
else
    echo -e "${RED}❌ 无法获取测试结果${NC}"
fi

echo ""
echo -e "📁 所有测试文件位于: ${GREEN}$SCRIPT_DIR${NC}"
echo -e "🌐 Web测试界面: ${GREEN}$BASE_URL/docs/test/api_test_web.html${NC}"
echo ""
echo -e "${GREEN}✅ 一键测试完成！${NC}"
