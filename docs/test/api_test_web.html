<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API接口测试工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .test-config {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }
        
        .form-group input, .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .test-results {
            margin-top: 30px;
        }
        
        .test-item {
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 8px;
            border-left: 4px solid #ddd;
        }
        
        .test-item.success {
            background: #d4edda;
            border-left-color: #28a745;
        }
        
        .test-item.error {
            background: #f8d7da;
            border-left-color: #dc3545;
        }
        
        .test-item.info {
            background: #d1ecf1;
            border-left-color: #17a2b8;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            width: 0%;
            transition: width 0.3s;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 API接口测试工具</h1>
            <p>全面测试所有API接口并提供自动修复建议</p>
        </div>
        
        <div class="content">
            <div class="test-config">
                <h3>测试配置</h3>
                <div class="form-group">
                    <label for="baseUrl">API基础地址:</label>
                    <input type="url" id="baseUrl" value="http://news.test.jijiaox.com" placeholder="http://localhost">
                </div>
                <div class="form-group">
                    <label for="autoFix">自动修复:</label>
                    <select id="autoFix">
                        <option value="false">禁用</option>
                        <option value="true">启用</option>
                    </select>
                </div>
                <button class="btn" onclick="startTest()" id="startBtn">开始测试</button>
            </div>
            
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>正在执行API测试，请稍候...</p>
            </div>
            
            <div class="test-results" id="results" style="display: none;">
                <h3>测试结果</h3>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="stats" id="stats"></div>
                <div id="testItems"></div>
            </div>
        </div>
    </div>

    <script>
        let testResults = [];
        let currentTest = 0;
        let totalTests = 0;

        async function startTest() {
            const baseUrl = document.getElementById('baseUrl').value;
            const autoFix = document.getElementById('autoFix').value === 'true';
            
            if (!baseUrl) {
                alert('请输入API基础地址');
                return;
            }
            
            // 重置状态
            testResults = [];
            currentTest = 0;
            totalTests = 0;
            
            // 显示加载状态
            document.getElementById('startBtn').disabled = true;
            document.getElementById('loading').style.display = 'block';
            document.getElementById('results').style.display = 'none';
            
            try {
                // 执行测试
                await runComprehensiveTest(baseUrl, autoFix);
            } catch (error) {
                addTestResult('测试执行', false, '测试执行失败: ' + error.message);
            }
            
            // 隐藏加载状态
            document.getElementById('loading').style.display = 'none';
            document.getElementById('results').style.display = 'block';
            document.getElementById('startBtn').disabled = false;
            
            // 显示最终结果
            displayFinalResults();
        }

        async function runComprehensiveTest(baseUrl, autoFix) {
            // 这里实现具体的测试逻辑
            // 由于浏览器环境限制，这里只是模拟测试过程
            
            const tests = [
                { name: '基础连接测试', endpoint: '/api/' },
                { name: '认证状态检查', endpoint: '/api/auth/check' },
                { name: '用户登录', endpoint: '/api/auth/login', method: 'POST' },
                { name: '获取用户列表', endpoint: '/api/users' },
                { name: '获取分类列表', endpoint: '/api/categories' },
                { name: '获取文章列表', endpoint: '/api/articles' },
                { name: '获取自定义字段', endpoint: '/api/article-custom-fields' },
                { name: '获取门户配置', endpoint: '/api/portal/configs/' },
                { name: '获取门户模块', endpoint: '/api/portal/modules/' },
                { name: '公开接口测试', endpoint: '/api/public-portal/modules/enabled' }
            ];
            
            totalTests = tests.length;
            
            for (let i = 0; i < tests.length; i++) {
                const test = tests[i];
                currentTest = i + 1;
                
                try {
                    const result = await executeApiTest(baseUrl, test);
                    addTestResult(test.name, result.success, result.message);
                } catch (error) {
                    addTestResult(test.name, false, error.message);
                }
                
                updateProgress();
                await sleep(500); // 模拟测试延迟
            }
        }

        async function executeApiTest(baseUrl, test) {
            const url = baseUrl + test.endpoint;
            const method = test.method || 'GET';
            
            try {
                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                return {
                    success: response.ok,
                    message: response.ok ? '请求成功' : `HTTP ${response.status}: ${data.message || '请求失败'}`
                };
            } catch (error) {
                return {
                    success: false,
                    message: '网络错误: ' + error.message
                };
            }
        }

        function addTestResult(name, success, message) {
            testResults.push({ name, success, message });
            
            const testItems = document.getElementById('testItems');
            const item = document.createElement('div');
            item.className = `test-item ${success ? 'success' : 'error'}`;
            item.innerHTML = `
                <strong>${success ? '✅' : '❌'} ${name}</strong><br>
                <small>${message}</small>
            `;
            testItems.appendChild(item);
        }

        function updateProgress() {
            const progress = (currentTest / totalTests) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
        }

        function displayFinalResults() {
            const passed = testResults.filter(r => r.success).length;
            const failed = testResults.filter(r => !r.success).length;
            const successRate = totalTests > 0 ? Math.round((passed / totalTests) * 100) : 0;
            
            const stats = document.getElementById('stats');
            stats.innerHTML = `
                <div class="stat-card">
                    <div class="stat-number">${totalTests}</div>
                    <div class="stat-label">总测试数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${passed}</div>
                    <div class="stat-label">通过数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${failed}</div>
                    <div class="stat-label">失败数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${successRate}%</div>
                    <div class="stat-label">成功率</div>
                </div>
            `;
        }

        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }
    </script>
</body>
</html>
