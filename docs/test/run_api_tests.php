<?php
/**
 * API测试运行脚本
 * 
 * 使用方法:
 * php run_api_tests.php [base_url] [--auto-fix]
 * 
 * 示例:
 * php run_api_tests.php http://news.test.jijiaox.com
 * php run_api_tests.php http://localhost --auto-fix
 */

require_once 'ComprehensiveApiTester.php';

// 获取命令行参数
$baseUrl = $argv[1] ?? 'http://news.test.jijiaox.com';
$autoFix = in_array('--auto-fix', $argv);

echo "🚀 启动API接口测试\n";
echo "目标地址: {$baseUrl}\n";
echo "自动修复: " . ($autoFix ? '启用' : '禁用') . "\n";
echo str_repeat("-", 50) . "\n\n";

try {
    $tester = new ComprehensiveApiTester($baseUrl, $autoFix);
    $tester->runAllTests();
} catch (Exception $e) {
    echo "❌ 测试运行失败: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n✅ 测试完成！\n";
