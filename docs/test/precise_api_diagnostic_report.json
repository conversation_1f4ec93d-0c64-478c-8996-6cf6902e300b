{"test_time": "2025-07-11 11:49:24", "summary": {"total_tests": 4, "real_json_responses": 4, "fake_json_responses": 0, "html_responses": 0, "other_responses": 0, "real_success_rate": 100}, "detailed_results": [{"url": "http://news.test.jijiaox.com/api/", "http_code": 200, "content_type": "application/json; charset=utf-8", "response_length": 24839, "is_actually_json": true, "is_actually_html": true, "json_data": "<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>新闻门户系统</title>\n    <meta name=\"keywords\" content=\"新闻,资讯,门户\">\n    <meta name=\"description\" content=\"专业的新闻资讯门户网站\">\n    <link rel=\"stylesheet\" href=\"/static/layui/css/layui.css\">\n    <style>\n        * { margin: 0; padding: 0; box-sizing: border-box; }\n        body { font-family: 'Microsoft YaHei', Arial, sans-serif; background-color: #f5f5f5; }\n\n        /* 头部样式 */\n        .header {\n            background: #fff;\n            box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n            position: sticky;\n            top: 0;\n            z-index: 1000;\n        }\n        .header-top {\n            background: #1890ff;\n            color: #fff;\n            padding: 8px 0;\n            font-size: 12px;\n        }\n        .header-main {\n            padding: 15px 0;\n        }\n        .logo {\n            font-size: 28px;\n            font-weight: bold;\n            color: #1890ff;\n            text-decoration: none;\n        }\n        .search-box {\n            position: relative;\n        }\n        .search-input {\n            width: 300px;\n            height: 40px;\n            border: 2px solid #1890ff;\n            border-radius: 20px;\n            padding: 0 50px 0 20px;\n            outline: none;\n        }\n        .search-btn {\n            position: absolute;\n            right: 5px;\n            top: 5px;\n            width: 30px;\n            height: 30px;\n            background: #1890ff;\n            border: none;\n            border-radius: 15px;\n            color: #fff;\n            cursor: pointer;\n        }\n\n        /* 导航样式 */\n        .nav-menu {\n            background: #fff;\n            border-top: 1px solid #e6e6e6;\n            padding: 0;\n        }\n        .nav-item {\n            display: inline-block;\n            padding: 15px 25px;\n            color: #333;\n            text-decoration: none;\n            transition: all 0.3s;\n            border-bottom: 3px solid transparent;\n        }\n        .nav-item:hover, .nav-item.active {\n            color: #1890ff;\n            border-bottom-color: #1890ff;\n        }\n\n        /* 主体内容 */\n        .main-content {\n            padding: 20px 0;\n        }\n        .module-container {\n            margin-bottom: 30px;\n            background: #fff;\n            border-radius: 8px;\n            overflow: hidden;\n            box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n        }\n        .module-header {\n            background: #fafafa;\n            padding: 15px 20px;\n            border-bottom: 1px solid #e6e6e6;\n            font-size: 18px;\n            font-weight: bold;\n            color: #333;\n        }\n        .module-body {\n            padding: 20px;\n        }\n\n        /* 轮播样式 */\n        .banner-item {\n            position: relative;\n            height: 400px;\n            background-size: cover;\n            background-position: center;\n            display: flex;\n            align-items: flex-end;\n        }\n        .banner-content {\n            background: linear-gradient(transparent, rgba(0,0,0,0.7));\n            color: #fff;\n            padding: 30px;\n            width: 100%;\n        }\n        .banner-title {\n            font-size: 24px;\n            font-weight: bold;\n            margin-bottom: 10px;\n            line-height: 1.4;\n        }\n        .banner-summary {\n            font-size: 14px;\n            opacity: 0.9;\n            line-height: 1.6;\n        }\n\n        /* 新闻列表样式 */\n        .news-list {\n            list-style: none;\n        }\n        .news-item {\n            display: flex;\n            padding: 15px 0;\n            border-bottom: 1px solid #f0f0f0;\n            transition: all 0.3s;\n        }\n        .news-item:hover {\n            background: #fafafa;\n            margin: 0 -20px;\n            padding: 15px 20px;\n        }\n        .news-item:last-child {\n            border-bottom: none;\n        }\n        .news-image {\n            width: 120px;\n            height: 80px;\n            background-size: cover;\n            background-position: center;\n            border-radius: 6px;\n            margin-right: 15px;\n            flex-shrink: 0;\n        }\n        .news-content {\n            flex: 1;\n        }\n        .news-title {\n            font-size: 16px;\n            font-weight: bold;\n            color: #333;\n            margin-bottom: 8px;\n            line-height: 1.4;\n            text-decoration: none;\n        }\n        .news-title:hover {\n            color: #1890ff;\n        }\n        .news-meta {\n            font-size: 12px;\n            color: #999;\n            margin-bottom: 8px;\n        }\n        .news-summary {\n            font-size: 14px;\n            color: #666;\n            line-height: 1.5;\n            display: -webkit-box;\n            -webkit-line-clamp: 2;\n            -webkit-box-orient: vertical;\n            overflow: hidden;\n        }\n\n        /* 分类导航样式 */\n        .category-nav {\n            display: flex;\n            flex-wrap: wrap;\n            gap: 15px;\n        }\n        .category-item {\n            padding: 10px 20px;\n            background: #f8f9fa;\n            border-radius: 20px;\n            color: #333;\n            text-decoration: none;\n            transition: all 0.3s;\n            border: 1px solid #e9ecef;\n        }\n        .category-item:hover {\n            background: #1890ff;\n            color: #fff;\n            border-color: #1890ff;\n        }\n\n        /* 广告样式 */\n        .ad-container {\n            text-align: center;\n            padding: 20px;\n            background: #f8f9fa;\n            border: 2px dashed #ddd;\n            border-radius: 8px;\n            color: #999;\n        }\n\n        /* 外链模块样式 */\n        .external-links {\n            background: #f8f9fa;\n            padding: 40px 0;\n            margin-top: 50px;\n            border-top: 1px solid #e6e6e6;\n        }\n        .links-header {\n            text-align: center;\n            margin-bottom: 30px;\n        }\n        .links-header h3 {\n            font-size: 24px;\n            color: #333;\n            margin: 0;\n            font-weight: bold;\n        }\n        .links-content {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n            gap: 30px;\n        }\n        .links-category {\n            background: #fff;\n            border-radius: 8px;\n            padding: 20px;\n            box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n        }\n        .category-title {\n            font-size: 16px;\n            font-weight: bold;\n            color: #333;\n            margin-bottom: 15px;\n            padding-bottom: 10px;\n            border-bottom: 2px solid #1890ff;\n        }\n        .links-list {\n            display: grid;\n            grid-template-columns: repeat(2, 1fr);\n            gap: 10px;\n        }\n        .external-link {\n            display: block;\n            padding: 8px 12px;\n            background: #f8f9fa;\n            color: #333;\n            text-decoration: none;\n            border-radius: 4px;\n            transition: all 0.3s;\n            font-size: 14px;\n            border: 1px solid #e9ecef;\n        }\n        .external-link:hover {\n            background: #1890ff;\n            color: #fff;\n            border-color: #1890ff;\n            transform: translateY(-1px);\n        }\n\n        /* 底部样式 */\n        .footer {\n            background: #333;\n            color: #fff;\n            padding: 20px 0;\n        }\n        .footer-bottom {\n            text-align: center;\n            color: #999;\n            font-size: 12px;\n        }\n        .footer-bottom a {\n            color: #999;\n            text-decoration: none;\n            margin: 0 5px;\n        }\n        .footer-bottom a:hover {\n            color: #1890ff;\n        }\n\n        /* 响应式布局 */\n        .module-one-column {\n            width: 100%;\n        }\n        .module-two-column {\n            width: 48%;\n            display: inline-block;\n            vertical-align: top;\n            margin-right: 2%;\n        }\n        .module-two-column:nth-child(2n) {\n            margin-right: 0;\n        }\n\n        /* 移动端适配 */\n        @media (max-width: 768px) {\n            .module-two-column {\n                width: 100%;\n                margin-right: 0;\n                margin-bottom: 20px;\n            }\n            .search-input {\n                width: 200px;\n            }\n            .banner-title {\n                font-size: 18px;\n            }\n            .news-image {\n                width: 80px;\n                height: 60px;\n            }\n            .links-content {\n                grid-template-columns: 1fr;\n                gap: 20px;\n            }\n            .links-list {\n                grid-template-columns: 1fr;\n            }\n            .external-links {\n                padding: 20px 0;\n            }\n        }\n\n        /* 错误提示样式 */\n        .error-message {\n            background: #fff2f0;\n            border: 1px solid #ffccc7;\n            color: #ff4d4f;\n            padding: 15px;\n            border-radius: 6px;\n            margin: 20px 0;\n        }\n    </style>\n</head>\n<body>\n    <!-- 头部 -->\n    <header class=\"header\">\n        <div class=\"header-top\">\n            <div class=\"layui-container\">\n                <div class=\"layui-row\">\n                    <div class=\"layui-col-md6\">\n                        <span>欢迎访问 新闻门户系统</span>\n                    </div>\n                    <div class=\"layui-col-md6\" style=\"text-align: right;\">\n                        <span id=\"currentTime\"></span>\n                    </div>\n                </div>\n            </div>\n        </div>\n\n        <div class=\"header-main\">\n            <div class=\"layui-container\">\n                <div class=\"layui-row layui-col-space15\">\n                    <div class=\"layui-col-md4\">\n                        <a href=\"/\" class=\"logo\">新闻门户系统</a>\n                    </div>\n                    <div class=\"layui-col-md8\" style=\"text-align: right;\">\n                        <div class=\"search-box\">\n                            <input type=\"text\" class=\"search-input\" placeholder=\"搜索新闻...\" id=\"searchInput\">\n                            <button class=\"search-btn\" onclick=\"searchNews()\">\n                                <i class=\"layui-icon layui-icon-search\"></i>\n                            </button>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n\n        <!-- 导航菜单 -->\n        <nav class=\"nav-menu\">\n            <div class=\"layui-container\">\n                <a href=\"/\" class=\"nav-item active\">首页</a>\n                                <a href=\"/category/1\" class=\"nav-item\">时政新闻</a>\n                                <a href=\"/category/2\" class=\"nav-item\">科技资讯</a>\n                                <a href=\"/category/3\" class=\"nav-item\">体育新闻</a>\n                                <a href=\"/category/4\" class=\"nav-item\">娱乐八卦</a>\n                                <a href=\"/category/5\" class=\"nav-item\">财经资讯</a>\n                                <a href=\"/category/6\" class=\"nav-item\">社会新闻</a>\n                            </div>\n        </nav>\n    </header>\n\n    <!-- 主体内容 -->\n    <main class=\"main-content\">\n        <div class=\"layui-container\">\n            \n            <!-- 动态渲染模块 -->\n                        <div class=\"module-container module-one-column\">\n                                        <!-- 新闻轮播模块 -->\n<div class=\"module-header\">新闻轮播</div>\n<div class=\"module-body\">\n    <div class=\"ad-container\">\n        <i class=\"layui-icon layui-icon-picture\" style=\"font-size: 48px; color: #ddd;\"></i>\n        <p>暂无轮播内容</p>\n        <p style=\"font-size: 12px; margin-top: 10px;\">请在后台添加带有封面图的新闻文章</p>\n    </div>\n</div>\n            </div>\n                        <div class=\"module-container module-two-column\">\n                                        <!-- 热门新闻模块 -->\n<div class=\"module-header\">\n    <i class=\"layui-icon layui-icon-fire\" style=\"color: #ff5722; margin-right: 8px;\"></i>\n    热门新闻</div>\n<div class=\"module-body\">\n        <div class=\"ad-container\">\n        <i class=\"layui-icon layui-icon-file\" style=\"font-size: 48px; color: #ddd;\"></i>\n        <p>暂无热门新闻</p>\n        <p style=\"font-size: 12px; margin-top: 10px;\">请在后台发布新闻文章</p>\n    </div>\n    </div>\n\n                                </div>\n                        <div class=\"module-container module-one-column\">\n                                        <!-- 分类导航模块 -->\n<div class=\"module-header\">\n    <i class=\"layui-icon layui-icon-app\" style=\"color: #1890ff; margin-right: 8px;\"></i>\n    分类导航</div>\n<div class=\"module-body\">\n        <div class=\"category-nav\">\n                <a href=\"/category/1\" class=\"category-item\">\n                        时政新闻        </a>\n                <a href=\"/category/2\" class=\"category-item\">\n                        科技资讯        </a>\n                <a href=\"/category/3\" class=\"category-item\">\n                        体育新闻        </a>\n                <a href=\"/category/4\" class=\"category-item\">\n                        娱乐八卦        </a>\n                <a href=\"/category/5\" class=\"category-item\">\n                        财经资讯        </a>\n                <a href=\"/category/6\" class=\"category-item\">\n                        社会新闻        </a>\n            </div>\n    </div>\n\n                                </div>\n                        <div class=\"module-container module-two-column\">\n                                        <!-- 最新文章模块 -->\n<div class=\"module-header\">\n    <i class=\"layui-icon layui-icon-release\" style=\"color: #52c41a; margin-right: 8px;\"></i>\n    最新文章</div>\n<div class=\"module-body\">\n        <div class=\"ad-container\">\n        <i class=\"layui-icon layui-icon-file\" style=\"font-size: 48px; color: #ddd;\"></i>\n        <p>暂无最新文章</p>\n        <p style=\"font-size: 12px; margin-top: 10px;\">请在后台发布新闻文章</p>\n    </div>\n    </div>\n\n                                </div>\n                        <div class=\"module-container module-one-column\">\n                                        <!-- 侧边广告模块 -->\n<div class=\"module-header\">\n    <i class=\"layui-icon layui-icon-notice\" style=\"color: #fa8c16; margin-right: 8px;\"></i>\n    侧边广告</div>\n<div class=\"module-body\">\n    <div class=\"ad-container\" style=\"min-height: 250px; display: flex; flex-direction: column; justify-content: center;\">\n        <i class=\"layui-icon layui-icon-picture\" style=\"font-size: 48px; color: #ddd; margin-bottom: 15px;\"></i>\n        <p style=\"font-size: 16px; margin-bottom: 10px;\">广告位招租</p>\n        <p style=\"font-size: 12px; color: #999;\">\n            尺寸：300px × 250px        </p>\n        <p style=\"font-size: 12px; color: #999; margin-top: 10px;\">\n            联系电话：400-123-4567\n        </p>\n    </div>\n</div>\n\n                                </div>\n                        <div class=\"module-container module-one-column\">\n                                        <div class=\"module-header\">底部链接</div>\n                        <div class=\"module-body\">\n                            <p>模块 \"footer_links\" 暂未实现</p>\n                        </div>\n                            </div>\n                    </div>\n    </main>\n\n    <!-- 外链模块 -->\n    <section class=\"external-links\">\n        <div class=\"layui-container\">\n            <div class=\"links-header\">\n                <h3>相关链接</h3>\n            </div>\n            <div class=\"links-content\">\n                <div class=\"links-category\">\n                    <div class=\"category-title\">新闻媒体</div>\n                    <div class=\"links-list\">\n                        <a href=\"https://www.xinhuanet.com\" target=\"_blank\" class=\"external-link\">新华网</a>\n                        <a href=\"https://www.people.com.cn\" target=\"_blank\" class=\"external-link\">人民网</a>\n                        <a href=\"https://www.cctv.com\" target=\"_blank\" class=\"external-link\">央视网</a>\n                        <a href=\"https://www.chinanews.com.cn\" target=\"_blank\" class=\"external-link\">中新网</a>\n                        <a href=\"https://www.gmw.cn\" target=\"_blank\" class=\"external-link\">光明网</a>\n                        <a href=\"https://www.chinadaily.com.cn\" target=\"_blank\" class=\"external-link\">中国日报</a>\n                    </div>\n                </div>\n\n                <div class=\"links-category\">\n                    <div class=\"category-title\">科技资讯</div>\n                    <div class=\"links-list\">\n                        <a href=\"https://www.36kr.com\" target=\"_blank\" class=\"external-link\">36氪</a>\n                        <a href=\"https://www.ithome.com\" target=\"_blank\" class=\"external-link\">IT之家</a>\n                        <a href=\"https://www.cnbeta.com\" target=\"_blank\" class=\"external-link\">cnBeta</a>\n                        <a href=\"https://www.pingwest.com\" target=\"_blank\" class=\"external-link\">PingWest</a>\n                        <a href=\"https://www.geekpark.net\" target=\"_blank\" class=\"external-link\">极客公园</a>\n                        <a href=\"https://www.leiphone.com\" target=\"_blank\" class=\"external-link\">雷锋网</a>\n                    </div>\n                </div>\n\n                <div class=\"links-category\">\n                    <div class=\"category-title\">财经金融</div>\n                    <div class=\"links-list\">\n                        <a href=\"https://www.caixin.com\" target=\"_blank\" class=\"external-link\">财新网</a>\n                        <a href=\"https://www.yicai.com\" target=\"_blank\" class=\"external-link\">第一财经</a>\n                        <a href=\"https://www.jiemian.com\" target=\"_blank\" class=\"external-link\">界面新闻</a>\n                        <a href=\"https://www.wallstreetcn.com\" target=\"_blank\" class=\"external-link\">华尔街见闻</a>\n                        <a href=\"https://www.eastmoney.com\" target=\"_blank\" class=\"external-link\">东方财富</a>\n                        <a href=\"https://www.jrj.com.cn\" target=\"_blank\" class=\"external-link\">金融界</a>\n                    </div>\n                </div>\n\n                <div class=\"links-category\">\n                    <div class=\"category-title\">开发者工具</div>\n                    <div class=\"links-list\">\n                        <a href=\"https://github.com\" target=\"_blank\" class=\"external-link\">GitHub</a>\n                        <a href=\"https://gitee.com\" target=\"_blank\" class=\"external-link\">Gitee</a>\n                        <a href=\"https://stackoverflow.com\" target=\"_blank\" class=\"external-link\">Stack Overflow</a>\n                        <a href=\"https://www.runoob.com\" target=\"_blank\" class=\"external-link\">菜鸟教程</a>\n                        <a href=\"https://developer.mozilla.org\" target=\"_blank\" class=\"external-link\">MDN</a>\n                        <a href=\"https://www.w3school.com.cn\" target=\"_blank\" class=\"external-link\">W3School</a>\n                    </div>\n                </div>\n\n                <div class=\"links-category\">\n                    <div class=\"category-title\">API服务</div>\n                    <div class=\"links-list\">\n                        <a href=\"/api/portal/modules/enabled\" target=\"_blank\" class=\"external-link\">门户模块API</a>\n                        <a href=\"/api/admin/portal/configs/groups\" target=\"_blank\" class=\"external-link\">配置分组API</a>\n                        <a href=\"/api/admin/portal/configs/\" target=\"_blank\" class=\"external-link\">配置列表API</a>\n                        <a href=\"/api/admin/portal/modules/\" target=\"_blank\" class=\"external-link\">模块管理API</a>\n                        <a href=\"/admin/portal_config.html\" target=\"_blank\" class=\"external-link\">配置管理后台</a>\n                        <a href=\"/admin/portal_module.html\" target=\"_blank\" class=\"external-link\">模块管理后台</a>\n                    </div>\n                </div>\n\n                <div class=\"links-category\">\n                    <div class=\"category-title\">实用工具</div>\n                    <div class=\"links-list\">\n                        <a href=\"https://www.baidu.com\" target=\"_blank\" class=\"external-link\">百度搜索</a>\n                        <a href=\"https://translate.google.com\" target=\"_blank\" class=\"external-link\">谷歌翻译</a>\n                        <a href=\"https://www.tianqi.com\" target=\"_blank\" class=\"external-link\">天气预报</a>\n                        <a href=\"https://map.baidu.com\" target=\"_blank\" class=\"external-link\">百度地图</a>\n                        <a href=\"https://www.12306.cn\" target=\"_blank\" class=\"external-link\">12306</a>\n                        <a href=\"https://www.sf-express.com\" target=\"_blank\" class=\"external-link\">顺丰速运</a>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </section>\n\n    <!-- 底部版权 -->\n    <footer class=\"footer\">\n        <div class=\"layui-container\">\n            <div class=\"footer-bottom\">\n                <p>&copy; 2025 新闻门户系统. 版权所有 |\n                   <a href=\"#\" style=\"color: #999;\">使用协议</a> |\n                   <a href=\"#\" style=\"color: #999;\">隐私政策</a> |\n                   <a href=\"#\" style=\"color: #999;\">联系我们</a>\n                </p>\n            </div>\n        </div>\n    </footer>\n\n    <script src=\"/static/layui/layui.js\"></script>\n    <script>\n        layui.use(['carousel', 'element'], function(){\n            var carousel = layui.carousel;\n            var element = layui.element;\n\n            // 初始化轮播\n            if (document.getElementById('newsBanner')) {\n                carousel.render({\n                    elem: '#newsBanner',\n                    width: '100%',\n                    height: '400px',\n                    interval: 5000,\n                    anim: 'fade'\n                });\n            }\n        });\n\n        // 更新时间\n        function updateTime() {\n            const now = new Date();\n            const timeStr = now.getFullYear() + '年' +\n                          (now.getMonth() + 1).toString().padStart(2, '0') + '月' +\n                          now.getDate().toString().padStart(2, '0') + '日 ' +\n                          now.getHours().toString().padStart(2, '0') + ':' +\n                          now.getMinutes().toString().padStart(2, '0');\n            document.getElementById('currentTime').textContent = timeStr;\n        }\n\n        // 搜索功能\n        function searchNews() {\n            const keyword = document.getElementById('searchInput').value.trim();\n            if (keyword) {\n                window.location.href = '/search?q=' + encodeURIComponent(keyword);\n            }\n        }\n\n        // 回车搜索\n        document.getElementById('searchInput').addEventListener('keypress', function(e) {\n            if (e.key === 'Enter') {\n                searchNews();\n            }\n        });\n\n        // 初始化\n        updateTime();\n        setInterval(updateTime, 60000); // 每分钟更新一次时间\n    </script>\n</body>\n</html>\n", "response_preview": "\"<!DOCTYPE html>\\n<html lang=\\\"zh-CN\\\">\\n<head>\\n    <meta charset=\\\"UTF-8\\\">\\n    <meta name=\\\"viewport\\\" content=\\\"width=device-width, initial-scale=1.0\\\">\\n    <title>新闻门户系统<\\/title>\\n ..."}, {"url": "http://news.test.jijiaox.com/api/test", "http_code": 200, "content_type": "application/json; charset=utf-8", "response_length": 24839, "is_actually_json": true, "is_actually_html": true, "json_data": "<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>新闻门户系统</title>\n    <meta name=\"keywords\" content=\"新闻,资讯,门户\">\n    <meta name=\"description\" content=\"专业的新闻资讯门户网站\">\n    <link rel=\"stylesheet\" href=\"/static/layui/css/layui.css\">\n    <style>\n        * { margin: 0; padding: 0; box-sizing: border-box; }\n        body { font-family: 'Microsoft YaHei', Arial, sans-serif; background-color: #f5f5f5; }\n\n        /* 头部样式 */\n        .header {\n            background: #fff;\n            box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n            position: sticky;\n            top: 0;\n            z-index: 1000;\n        }\n        .header-top {\n            background: #1890ff;\n            color: #fff;\n            padding: 8px 0;\n            font-size: 12px;\n        }\n        .header-main {\n            padding: 15px 0;\n        }\n        .logo {\n            font-size: 28px;\n            font-weight: bold;\n            color: #1890ff;\n            text-decoration: none;\n        }\n        .search-box {\n            position: relative;\n        }\n        .search-input {\n            width: 300px;\n            height: 40px;\n            border: 2px solid #1890ff;\n            border-radius: 20px;\n            padding: 0 50px 0 20px;\n            outline: none;\n        }\n        .search-btn {\n            position: absolute;\n            right: 5px;\n            top: 5px;\n            width: 30px;\n            height: 30px;\n            background: #1890ff;\n            border: none;\n            border-radius: 15px;\n            color: #fff;\n            cursor: pointer;\n        }\n\n        /* 导航样式 */\n        .nav-menu {\n            background: #fff;\n            border-top: 1px solid #e6e6e6;\n            padding: 0;\n        }\n        .nav-item {\n            display: inline-block;\n            padding: 15px 25px;\n            color: #333;\n            text-decoration: none;\n            transition: all 0.3s;\n            border-bottom: 3px solid transparent;\n        }\n        .nav-item:hover, .nav-item.active {\n            color: #1890ff;\n            border-bottom-color: #1890ff;\n        }\n\n        /* 主体内容 */\n        .main-content {\n            padding: 20px 0;\n        }\n        .module-container {\n            margin-bottom: 30px;\n            background: #fff;\n            border-radius: 8px;\n            overflow: hidden;\n            box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n        }\n        .module-header {\n            background: #fafafa;\n            padding: 15px 20px;\n            border-bottom: 1px solid #e6e6e6;\n            font-size: 18px;\n            font-weight: bold;\n            color: #333;\n        }\n        .module-body {\n            padding: 20px;\n        }\n\n        /* 轮播样式 */\n        .banner-item {\n            position: relative;\n            height: 400px;\n            background-size: cover;\n            background-position: center;\n            display: flex;\n            align-items: flex-end;\n        }\n        .banner-content {\n            background: linear-gradient(transparent, rgba(0,0,0,0.7));\n            color: #fff;\n            padding: 30px;\n            width: 100%;\n        }\n        .banner-title {\n            font-size: 24px;\n            font-weight: bold;\n            margin-bottom: 10px;\n            line-height: 1.4;\n        }\n        .banner-summary {\n            font-size: 14px;\n            opacity: 0.9;\n            line-height: 1.6;\n        }\n\n        /* 新闻列表样式 */\n        .news-list {\n            list-style: none;\n        }\n        .news-item {\n            display: flex;\n            padding: 15px 0;\n            border-bottom: 1px solid #f0f0f0;\n            transition: all 0.3s;\n        }\n        .news-item:hover {\n            background: #fafafa;\n            margin: 0 -20px;\n            padding: 15px 20px;\n        }\n        .news-item:last-child {\n            border-bottom: none;\n        }\n        .news-image {\n            width: 120px;\n            height: 80px;\n            background-size: cover;\n            background-position: center;\n            border-radius: 6px;\n            margin-right: 15px;\n            flex-shrink: 0;\n        }\n        .news-content {\n            flex: 1;\n        }\n        .news-title {\n            font-size: 16px;\n            font-weight: bold;\n            color: #333;\n            margin-bottom: 8px;\n            line-height: 1.4;\n            text-decoration: none;\n        }\n        .news-title:hover {\n            color: #1890ff;\n        }\n        .news-meta {\n            font-size: 12px;\n            color: #999;\n            margin-bottom: 8px;\n        }\n        .news-summary {\n            font-size: 14px;\n            color: #666;\n            line-height: 1.5;\n            display: -webkit-box;\n            -webkit-line-clamp: 2;\n            -webkit-box-orient: vertical;\n            overflow: hidden;\n        }\n\n        /* 分类导航样式 */\n        .category-nav {\n            display: flex;\n            flex-wrap: wrap;\n            gap: 15px;\n        }\n        .category-item {\n            padding: 10px 20px;\n            background: #f8f9fa;\n            border-radius: 20px;\n            color: #333;\n            text-decoration: none;\n            transition: all 0.3s;\n            border: 1px solid #e9ecef;\n        }\n        .category-item:hover {\n            background: #1890ff;\n            color: #fff;\n            border-color: #1890ff;\n        }\n\n        /* 广告样式 */\n        .ad-container {\n            text-align: center;\n            padding: 20px;\n            background: #f8f9fa;\n            border: 2px dashed #ddd;\n            border-radius: 8px;\n            color: #999;\n        }\n\n        /* 外链模块样式 */\n        .external-links {\n            background: #f8f9fa;\n            padding: 40px 0;\n            margin-top: 50px;\n            border-top: 1px solid #e6e6e6;\n        }\n        .links-header {\n            text-align: center;\n            margin-bottom: 30px;\n        }\n        .links-header h3 {\n            font-size: 24px;\n            color: #333;\n            margin: 0;\n            font-weight: bold;\n        }\n        .links-content {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n            gap: 30px;\n        }\n        .links-category {\n            background: #fff;\n            border-radius: 8px;\n            padding: 20px;\n            box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n        }\n        .category-title {\n            font-size: 16px;\n            font-weight: bold;\n            color: #333;\n            margin-bottom: 15px;\n            padding-bottom: 10px;\n            border-bottom: 2px solid #1890ff;\n        }\n        .links-list {\n            display: grid;\n            grid-template-columns: repeat(2, 1fr);\n            gap: 10px;\n        }\n        .external-link {\n            display: block;\n            padding: 8px 12px;\n            background: #f8f9fa;\n            color: #333;\n            text-decoration: none;\n            border-radius: 4px;\n            transition: all 0.3s;\n            font-size: 14px;\n            border: 1px solid #e9ecef;\n        }\n        .external-link:hover {\n            background: #1890ff;\n            color: #fff;\n            border-color: #1890ff;\n            transform: translateY(-1px);\n        }\n\n        /* 底部样式 */\n        .footer {\n            background: #333;\n            color: #fff;\n            padding: 20px 0;\n        }\n        .footer-bottom {\n            text-align: center;\n            color: #999;\n            font-size: 12px;\n        }\n        .footer-bottom a {\n            color: #999;\n            text-decoration: none;\n            margin: 0 5px;\n        }\n        .footer-bottom a:hover {\n            color: #1890ff;\n        }\n\n        /* 响应式布局 */\n        .module-one-column {\n            width: 100%;\n        }\n        .module-two-column {\n            width: 48%;\n            display: inline-block;\n            vertical-align: top;\n            margin-right: 2%;\n        }\n        .module-two-column:nth-child(2n) {\n            margin-right: 0;\n        }\n\n        /* 移动端适配 */\n        @media (max-width: 768px) {\n            .module-two-column {\n                width: 100%;\n                margin-right: 0;\n                margin-bottom: 20px;\n            }\n            .search-input {\n                width: 200px;\n            }\n            .banner-title {\n                font-size: 18px;\n            }\n            .news-image {\n                width: 80px;\n                height: 60px;\n            }\n            .links-content {\n                grid-template-columns: 1fr;\n                gap: 20px;\n            }\n            .links-list {\n                grid-template-columns: 1fr;\n            }\n            .external-links {\n                padding: 20px 0;\n            }\n        }\n\n        /* 错误提示样式 */\n        .error-message {\n            background: #fff2f0;\n            border: 1px solid #ffccc7;\n            color: #ff4d4f;\n            padding: 15px;\n            border-radius: 6px;\n            margin: 20px 0;\n        }\n    </style>\n</head>\n<body>\n    <!-- 头部 -->\n    <header class=\"header\">\n        <div class=\"header-top\">\n            <div class=\"layui-container\">\n                <div class=\"layui-row\">\n                    <div class=\"layui-col-md6\">\n                        <span>欢迎访问 新闻门户系统</span>\n                    </div>\n                    <div class=\"layui-col-md6\" style=\"text-align: right;\">\n                        <span id=\"currentTime\"></span>\n                    </div>\n                </div>\n            </div>\n        </div>\n\n        <div class=\"header-main\">\n            <div class=\"layui-container\">\n                <div class=\"layui-row layui-col-space15\">\n                    <div class=\"layui-col-md4\">\n                        <a href=\"/\" class=\"logo\">新闻门户系统</a>\n                    </div>\n                    <div class=\"layui-col-md8\" style=\"text-align: right;\">\n                        <div class=\"search-box\">\n                            <input type=\"text\" class=\"search-input\" placeholder=\"搜索新闻...\" id=\"searchInput\">\n                            <button class=\"search-btn\" onclick=\"searchNews()\">\n                                <i class=\"layui-icon layui-icon-search\"></i>\n                            </button>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n\n        <!-- 导航菜单 -->\n        <nav class=\"nav-menu\">\n            <div class=\"layui-container\">\n                <a href=\"/\" class=\"nav-item active\">首页</a>\n                                <a href=\"/category/1\" class=\"nav-item\">时政新闻</a>\n                                <a href=\"/category/2\" class=\"nav-item\">科技资讯</a>\n                                <a href=\"/category/3\" class=\"nav-item\">体育新闻</a>\n                                <a href=\"/category/4\" class=\"nav-item\">娱乐八卦</a>\n                                <a href=\"/category/5\" class=\"nav-item\">财经资讯</a>\n                                <a href=\"/category/6\" class=\"nav-item\">社会新闻</a>\n                            </div>\n        </nav>\n    </header>\n\n    <!-- 主体内容 -->\n    <main class=\"main-content\">\n        <div class=\"layui-container\">\n            \n            <!-- 动态渲染模块 -->\n                        <div class=\"module-container module-one-column\">\n                                        <!-- 新闻轮播模块 -->\n<div class=\"module-header\">新闻轮播</div>\n<div class=\"module-body\">\n    <div class=\"ad-container\">\n        <i class=\"layui-icon layui-icon-picture\" style=\"font-size: 48px; color: #ddd;\"></i>\n        <p>暂无轮播内容</p>\n        <p style=\"font-size: 12px; margin-top: 10px;\">请在后台添加带有封面图的新闻文章</p>\n    </div>\n</div>\n            </div>\n                        <div class=\"module-container module-two-column\">\n                                        <!-- 热门新闻模块 -->\n<div class=\"module-header\">\n    <i class=\"layui-icon layui-icon-fire\" style=\"color: #ff5722; margin-right: 8px;\"></i>\n    热门新闻</div>\n<div class=\"module-body\">\n        <div class=\"ad-container\">\n        <i class=\"layui-icon layui-icon-file\" style=\"font-size: 48px; color: #ddd;\"></i>\n        <p>暂无热门新闻</p>\n        <p style=\"font-size: 12px; margin-top: 10px;\">请在后台发布新闻文章</p>\n    </div>\n    </div>\n\n                                </div>\n                        <div class=\"module-container module-one-column\">\n                                        <!-- 分类导航模块 -->\n<div class=\"module-header\">\n    <i class=\"layui-icon layui-icon-app\" style=\"color: #1890ff; margin-right: 8px;\"></i>\n    分类导航</div>\n<div class=\"module-body\">\n        <div class=\"category-nav\">\n                <a href=\"/category/1\" class=\"category-item\">\n                        时政新闻        </a>\n                <a href=\"/category/2\" class=\"category-item\">\n                        科技资讯        </a>\n                <a href=\"/category/3\" class=\"category-item\">\n                        体育新闻        </a>\n                <a href=\"/category/4\" class=\"category-item\">\n                        娱乐八卦        </a>\n                <a href=\"/category/5\" class=\"category-item\">\n                        财经资讯        </a>\n                <a href=\"/category/6\" class=\"category-item\">\n                        社会新闻        </a>\n            </div>\n    </div>\n\n                                </div>\n                        <div class=\"module-container module-two-column\">\n                                        <!-- 最新文章模块 -->\n<div class=\"module-header\">\n    <i class=\"layui-icon layui-icon-release\" style=\"color: #52c41a; margin-right: 8px;\"></i>\n    最新文章</div>\n<div class=\"module-body\">\n        <div class=\"ad-container\">\n        <i class=\"layui-icon layui-icon-file\" style=\"font-size: 48px; color: #ddd;\"></i>\n        <p>暂无最新文章</p>\n        <p style=\"font-size: 12px; margin-top: 10px;\">请在后台发布新闻文章</p>\n    </div>\n    </div>\n\n                                </div>\n                        <div class=\"module-container module-one-column\">\n                                        <!-- 侧边广告模块 -->\n<div class=\"module-header\">\n    <i class=\"layui-icon layui-icon-notice\" style=\"color: #fa8c16; margin-right: 8px;\"></i>\n    侧边广告</div>\n<div class=\"module-body\">\n    <div class=\"ad-container\" style=\"min-height: 250px; display: flex; flex-direction: column; justify-content: center;\">\n        <i class=\"layui-icon layui-icon-picture\" style=\"font-size: 48px; color: #ddd; margin-bottom: 15px;\"></i>\n        <p style=\"font-size: 16px; margin-bottom: 10px;\">广告位招租</p>\n        <p style=\"font-size: 12px; color: #999;\">\n            尺寸：300px × 250px        </p>\n        <p style=\"font-size: 12px; color: #999; margin-top: 10px;\">\n            联系电话：400-123-4567\n        </p>\n    </div>\n</div>\n\n                                </div>\n                        <div class=\"module-container module-one-column\">\n                                        <div class=\"module-header\">底部链接</div>\n                        <div class=\"module-body\">\n                            <p>模块 \"footer_links\" 暂未实现</p>\n                        </div>\n                            </div>\n                    </div>\n    </main>\n\n    <!-- 外链模块 -->\n    <section class=\"external-links\">\n        <div class=\"layui-container\">\n            <div class=\"links-header\">\n                <h3>相关链接</h3>\n            </div>\n            <div class=\"links-content\">\n                <div class=\"links-category\">\n                    <div class=\"category-title\">新闻媒体</div>\n                    <div class=\"links-list\">\n                        <a href=\"https://www.xinhuanet.com\" target=\"_blank\" class=\"external-link\">新华网</a>\n                        <a href=\"https://www.people.com.cn\" target=\"_blank\" class=\"external-link\">人民网</a>\n                        <a href=\"https://www.cctv.com\" target=\"_blank\" class=\"external-link\">央视网</a>\n                        <a href=\"https://www.chinanews.com.cn\" target=\"_blank\" class=\"external-link\">中新网</a>\n                        <a href=\"https://www.gmw.cn\" target=\"_blank\" class=\"external-link\">光明网</a>\n                        <a href=\"https://www.chinadaily.com.cn\" target=\"_blank\" class=\"external-link\">中国日报</a>\n                    </div>\n                </div>\n\n                <div class=\"links-category\">\n                    <div class=\"category-title\">科技资讯</div>\n                    <div class=\"links-list\">\n                        <a href=\"https://www.36kr.com\" target=\"_blank\" class=\"external-link\">36氪</a>\n                        <a href=\"https://www.ithome.com\" target=\"_blank\" class=\"external-link\">IT之家</a>\n                        <a href=\"https://www.cnbeta.com\" target=\"_blank\" class=\"external-link\">cnBeta</a>\n                        <a href=\"https://www.pingwest.com\" target=\"_blank\" class=\"external-link\">PingWest</a>\n                        <a href=\"https://www.geekpark.net\" target=\"_blank\" class=\"external-link\">极客公园</a>\n                        <a href=\"https://www.leiphone.com\" target=\"_blank\" class=\"external-link\">雷锋网</a>\n                    </div>\n                </div>\n\n                <div class=\"links-category\">\n                    <div class=\"category-title\">财经金融</div>\n                    <div class=\"links-list\">\n                        <a href=\"https://www.caixin.com\" target=\"_blank\" class=\"external-link\">财新网</a>\n                        <a href=\"https://www.yicai.com\" target=\"_blank\" class=\"external-link\">第一财经</a>\n                        <a href=\"https://www.jiemian.com\" target=\"_blank\" class=\"external-link\">界面新闻</a>\n                        <a href=\"https://www.wallstreetcn.com\" target=\"_blank\" class=\"external-link\">华尔街见闻</a>\n                        <a href=\"https://www.eastmoney.com\" target=\"_blank\" class=\"external-link\">东方财富</a>\n                        <a href=\"https://www.jrj.com.cn\" target=\"_blank\" class=\"external-link\">金融界</a>\n                    </div>\n                </div>\n\n                <div class=\"links-category\">\n                    <div class=\"category-title\">开发者工具</div>\n                    <div class=\"links-list\">\n                        <a href=\"https://github.com\" target=\"_blank\" class=\"external-link\">GitHub</a>\n                        <a href=\"https://gitee.com\" target=\"_blank\" class=\"external-link\">Gitee</a>\n                        <a href=\"https://stackoverflow.com\" target=\"_blank\" class=\"external-link\">Stack Overflow</a>\n                        <a href=\"https://www.runoob.com\" target=\"_blank\" class=\"external-link\">菜鸟教程</a>\n                        <a href=\"https://developer.mozilla.org\" target=\"_blank\" class=\"external-link\">MDN</a>\n                        <a href=\"https://www.w3school.com.cn\" target=\"_blank\" class=\"external-link\">W3School</a>\n                    </div>\n                </div>\n\n                <div class=\"links-category\">\n                    <div class=\"category-title\">API服务</div>\n                    <div class=\"links-list\">\n                        <a href=\"/api/portal/modules/enabled\" target=\"_blank\" class=\"external-link\">门户模块API</a>\n                        <a href=\"/api/admin/portal/configs/groups\" target=\"_blank\" class=\"external-link\">配置分组API</a>\n                        <a href=\"/api/admin/portal/configs/\" target=\"_blank\" class=\"external-link\">配置列表API</a>\n                        <a href=\"/api/admin/portal/modules/\" target=\"_blank\" class=\"external-link\">模块管理API</a>\n                        <a href=\"/admin/portal_config.html\" target=\"_blank\" class=\"external-link\">配置管理后台</a>\n                        <a href=\"/admin/portal_module.html\" target=\"_blank\" class=\"external-link\">模块管理后台</a>\n                    </div>\n                </div>\n\n                <div class=\"links-category\">\n                    <div class=\"category-title\">实用工具</div>\n                    <div class=\"links-list\">\n                        <a href=\"https://www.baidu.com\" target=\"_blank\" class=\"external-link\">百度搜索</a>\n                        <a href=\"https://translate.google.com\" target=\"_blank\" class=\"external-link\">谷歌翻译</a>\n                        <a href=\"https://www.tianqi.com\" target=\"_blank\" class=\"external-link\">天气预报</a>\n                        <a href=\"https://map.baidu.com\" target=\"_blank\" class=\"external-link\">百度地图</a>\n                        <a href=\"https://www.12306.cn\" target=\"_blank\" class=\"external-link\">12306</a>\n                        <a href=\"https://www.sf-express.com\" target=\"_blank\" class=\"external-link\">顺丰速运</a>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </section>\n\n    <!-- 底部版权 -->\n    <footer class=\"footer\">\n        <div class=\"layui-container\">\n            <div class=\"footer-bottom\">\n                <p>&copy; 2025 新闻门户系统. 版权所有 |\n                   <a href=\"#\" style=\"color: #999;\">使用协议</a> |\n                   <a href=\"#\" style=\"color: #999;\">隐私政策</a> |\n                   <a href=\"#\" style=\"color: #999;\">联系我们</a>\n                </p>\n            </div>\n        </div>\n    </footer>\n\n    <script src=\"/static/layui/layui.js\"></script>\n    <script>\n        layui.use(['carousel', 'element'], function(){\n            var carousel = layui.carousel;\n            var element = layui.element;\n\n            // 初始化轮播\n            if (document.getElementById('newsBanner')) {\n                carousel.render({\n                    elem: '#newsBanner',\n                    width: '100%',\n                    height: '400px',\n                    interval: 5000,\n                    anim: 'fade'\n                });\n            }\n        });\n\n        // 更新时间\n        function updateTime() {\n            const now = new Date();\n            const timeStr = now.getFullYear() + '年' +\n                          (now.getMonth() + 1).toString().padStart(2, '0') + '月' +\n                          now.getDate().toString().padStart(2, '0') + '日 ' +\n                          now.getHours().toString().padStart(2, '0') + ':' +\n                          now.getMinutes().toString().padStart(2, '0');\n            document.getElementById('currentTime').textContent = timeStr;\n        }\n\n        // 搜索功能\n        function searchNews() {\n            const keyword = document.getElementById('searchInput').value.trim();\n            if (keyword) {\n                window.location.href = '/search?q=' + encodeURIComponent(keyword);\n            }\n        }\n\n        // 回车搜索\n        document.getElementById('searchInput').addEventListener('keypress', function(e) {\n            if (e.key === 'Enter') {\n                searchNews();\n            }\n        });\n\n        // 初始化\n        updateTime();\n        setInterval(updateTime, 60000); // 每分钟更新一次时间\n    </script>\n</body>\n</html>\n", "response_preview": "\"<!DOCTYPE html>\\n<html lang=\\\"zh-CN\\\">\\n<head>\\n    <meta charset=\\\"UTF-8\\\">\\n    <meta name=\\\"viewport\\\" content=\\\"width=device-width, initial-scale=1.0\\\">\\n    <title>新闻门户系统<\\/title>\\n ..."}, {"url": "http://news.test.jijiaox.com/api/auth/check", "http_code": 200, "content_type": "application/json; charset=utf-8", "response_length": 24839, "is_actually_json": true, "is_actually_html": true, "json_data": "<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>新闻门户系统</title>\n    <meta name=\"keywords\" content=\"新闻,资讯,门户\">\n    <meta name=\"description\" content=\"专业的新闻资讯门户网站\">\n    <link rel=\"stylesheet\" href=\"/static/layui/css/layui.css\">\n    <style>\n        * { margin: 0; padding: 0; box-sizing: border-box; }\n        body { font-family: 'Microsoft YaHei', Arial, sans-serif; background-color: #f5f5f5; }\n\n        /* 头部样式 */\n        .header {\n            background: #fff;\n            box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n            position: sticky;\n            top: 0;\n            z-index: 1000;\n        }\n        .header-top {\n            background: #1890ff;\n            color: #fff;\n            padding: 8px 0;\n            font-size: 12px;\n        }\n        .header-main {\n            padding: 15px 0;\n        }\n        .logo {\n            font-size: 28px;\n            font-weight: bold;\n            color: #1890ff;\n            text-decoration: none;\n        }\n        .search-box {\n            position: relative;\n        }\n        .search-input {\n            width: 300px;\n            height: 40px;\n            border: 2px solid #1890ff;\n            border-radius: 20px;\n            padding: 0 50px 0 20px;\n            outline: none;\n        }\n        .search-btn {\n            position: absolute;\n            right: 5px;\n            top: 5px;\n            width: 30px;\n            height: 30px;\n            background: #1890ff;\n            border: none;\n            border-radius: 15px;\n            color: #fff;\n            cursor: pointer;\n        }\n\n        /* 导航样式 */\n        .nav-menu {\n            background: #fff;\n            border-top: 1px solid #e6e6e6;\n            padding: 0;\n        }\n        .nav-item {\n            display: inline-block;\n            padding: 15px 25px;\n            color: #333;\n            text-decoration: none;\n            transition: all 0.3s;\n            border-bottom: 3px solid transparent;\n        }\n        .nav-item:hover, .nav-item.active {\n            color: #1890ff;\n            border-bottom-color: #1890ff;\n        }\n\n        /* 主体内容 */\n        .main-content {\n            padding: 20px 0;\n        }\n        .module-container {\n            margin-bottom: 30px;\n            background: #fff;\n            border-radius: 8px;\n            overflow: hidden;\n            box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n        }\n        .module-header {\n            background: #fafafa;\n            padding: 15px 20px;\n            border-bottom: 1px solid #e6e6e6;\n            font-size: 18px;\n            font-weight: bold;\n            color: #333;\n        }\n        .module-body {\n            padding: 20px;\n        }\n\n        /* 轮播样式 */\n        .banner-item {\n            position: relative;\n            height: 400px;\n            background-size: cover;\n            background-position: center;\n            display: flex;\n            align-items: flex-end;\n        }\n        .banner-content {\n            background: linear-gradient(transparent, rgba(0,0,0,0.7));\n            color: #fff;\n            padding: 30px;\n            width: 100%;\n        }\n        .banner-title {\n            font-size: 24px;\n            font-weight: bold;\n            margin-bottom: 10px;\n            line-height: 1.4;\n        }\n        .banner-summary {\n            font-size: 14px;\n            opacity: 0.9;\n            line-height: 1.6;\n        }\n\n        /* 新闻列表样式 */\n        .news-list {\n            list-style: none;\n        }\n        .news-item {\n            display: flex;\n            padding: 15px 0;\n            border-bottom: 1px solid #f0f0f0;\n            transition: all 0.3s;\n        }\n        .news-item:hover {\n            background: #fafafa;\n            margin: 0 -20px;\n            padding: 15px 20px;\n        }\n        .news-item:last-child {\n            border-bottom: none;\n        }\n        .news-image {\n            width: 120px;\n            height: 80px;\n            background-size: cover;\n            background-position: center;\n            border-radius: 6px;\n            margin-right: 15px;\n            flex-shrink: 0;\n        }\n        .news-content {\n            flex: 1;\n        }\n        .news-title {\n            font-size: 16px;\n            font-weight: bold;\n            color: #333;\n            margin-bottom: 8px;\n            line-height: 1.4;\n            text-decoration: none;\n        }\n        .news-title:hover {\n            color: #1890ff;\n        }\n        .news-meta {\n            font-size: 12px;\n            color: #999;\n            margin-bottom: 8px;\n        }\n        .news-summary {\n            font-size: 14px;\n            color: #666;\n            line-height: 1.5;\n            display: -webkit-box;\n            -webkit-line-clamp: 2;\n            -webkit-box-orient: vertical;\n            overflow: hidden;\n        }\n\n        /* 分类导航样式 */\n        .category-nav {\n            display: flex;\n            flex-wrap: wrap;\n            gap: 15px;\n        }\n        .category-item {\n            padding: 10px 20px;\n            background: #f8f9fa;\n            border-radius: 20px;\n            color: #333;\n            text-decoration: none;\n            transition: all 0.3s;\n            border: 1px solid #e9ecef;\n        }\n        .category-item:hover {\n            background: #1890ff;\n            color: #fff;\n            border-color: #1890ff;\n        }\n\n        /* 广告样式 */\n        .ad-container {\n            text-align: center;\n            padding: 20px;\n            background: #f8f9fa;\n            border: 2px dashed #ddd;\n            border-radius: 8px;\n            color: #999;\n        }\n\n        /* 外链模块样式 */\n        .external-links {\n            background: #f8f9fa;\n            padding: 40px 0;\n            margin-top: 50px;\n            border-top: 1px solid #e6e6e6;\n        }\n        .links-header {\n            text-align: center;\n            margin-bottom: 30px;\n        }\n        .links-header h3 {\n            font-size: 24px;\n            color: #333;\n            margin: 0;\n            font-weight: bold;\n        }\n        .links-content {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n            gap: 30px;\n        }\n        .links-category {\n            background: #fff;\n            border-radius: 8px;\n            padding: 20px;\n            box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n        }\n        .category-title {\n            font-size: 16px;\n            font-weight: bold;\n            color: #333;\n            margin-bottom: 15px;\n            padding-bottom: 10px;\n            border-bottom: 2px solid #1890ff;\n        }\n        .links-list {\n            display: grid;\n            grid-template-columns: repeat(2, 1fr);\n            gap: 10px;\n        }\n        .external-link {\n            display: block;\n            padding: 8px 12px;\n            background: #f8f9fa;\n            color: #333;\n            text-decoration: none;\n            border-radius: 4px;\n            transition: all 0.3s;\n            font-size: 14px;\n            border: 1px solid #e9ecef;\n        }\n        .external-link:hover {\n            background: #1890ff;\n            color: #fff;\n            border-color: #1890ff;\n            transform: translateY(-1px);\n        }\n\n        /* 底部样式 */\n        .footer {\n            background: #333;\n            color: #fff;\n            padding: 20px 0;\n        }\n        .footer-bottom {\n            text-align: center;\n            color: #999;\n            font-size: 12px;\n        }\n        .footer-bottom a {\n            color: #999;\n            text-decoration: none;\n            margin: 0 5px;\n        }\n        .footer-bottom a:hover {\n            color: #1890ff;\n        }\n\n        /* 响应式布局 */\n        .module-one-column {\n            width: 100%;\n        }\n        .module-two-column {\n            width: 48%;\n            display: inline-block;\n            vertical-align: top;\n            margin-right: 2%;\n        }\n        .module-two-column:nth-child(2n) {\n            margin-right: 0;\n        }\n\n        /* 移动端适配 */\n        @media (max-width: 768px) {\n            .module-two-column {\n                width: 100%;\n                margin-right: 0;\n                margin-bottom: 20px;\n            }\n            .search-input {\n                width: 200px;\n            }\n            .banner-title {\n                font-size: 18px;\n            }\n            .news-image {\n                width: 80px;\n                height: 60px;\n            }\n            .links-content {\n                grid-template-columns: 1fr;\n                gap: 20px;\n            }\n            .links-list {\n                grid-template-columns: 1fr;\n            }\n            .external-links {\n                padding: 20px 0;\n            }\n        }\n\n        /* 错误提示样式 */\n        .error-message {\n            background: #fff2f0;\n            border: 1px solid #ffccc7;\n            color: #ff4d4f;\n            padding: 15px;\n            border-radius: 6px;\n            margin: 20px 0;\n        }\n    </style>\n</head>\n<body>\n    <!-- 头部 -->\n    <header class=\"header\">\n        <div class=\"header-top\">\n            <div class=\"layui-container\">\n                <div class=\"layui-row\">\n                    <div class=\"layui-col-md6\">\n                        <span>欢迎访问 新闻门户系统</span>\n                    </div>\n                    <div class=\"layui-col-md6\" style=\"text-align: right;\">\n                        <span id=\"currentTime\"></span>\n                    </div>\n                </div>\n            </div>\n        </div>\n\n        <div class=\"header-main\">\n            <div class=\"layui-container\">\n                <div class=\"layui-row layui-col-space15\">\n                    <div class=\"layui-col-md4\">\n                        <a href=\"/\" class=\"logo\">新闻门户系统</a>\n                    </div>\n                    <div class=\"layui-col-md8\" style=\"text-align: right;\">\n                        <div class=\"search-box\">\n                            <input type=\"text\" class=\"search-input\" placeholder=\"搜索新闻...\" id=\"searchInput\">\n                            <button class=\"search-btn\" onclick=\"searchNews()\">\n                                <i class=\"layui-icon layui-icon-search\"></i>\n                            </button>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n\n        <!-- 导航菜单 -->\n        <nav class=\"nav-menu\">\n            <div class=\"layui-container\">\n                <a href=\"/\" class=\"nav-item active\">首页</a>\n                                <a href=\"/category/1\" class=\"nav-item\">时政新闻</a>\n                                <a href=\"/category/2\" class=\"nav-item\">科技资讯</a>\n                                <a href=\"/category/3\" class=\"nav-item\">体育新闻</a>\n                                <a href=\"/category/4\" class=\"nav-item\">娱乐八卦</a>\n                                <a href=\"/category/5\" class=\"nav-item\">财经资讯</a>\n                                <a href=\"/category/6\" class=\"nav-item\">社会新闻</a>\n                            </div>\n        </nav>\n    </header>\n\n    <!-- 主体内容 -->\n    <main class=\"main-content\">\n        <div class=\"layui-container\">\n            \n            <!-- 动态渲染模块 -->\n                        <div class=\"module-container module-one-column\">\n                                        <!-- 新闻轮播模块 -->\n<div class=\"module-header\">新闻轮播</div>\n<div class=\"module-body\">\n    <div class=\"ad-container\">\n        <i class=\"layui-icon layui-icon-picture\" style=\"font-size: 48px; color: #ddd;\"></i>\n        <p>暂无轮播内容</p>\n        <p style=\"font-size: 12px; margin-top: 10px;\">请在后台添加带有封面图的新闻文章</p>\n    </div>\n</div>\n            </div>\n                        <div class=\"module-container module-two-column\">\n                                        <!-- 热门新闻模块 -->\n<div class=\"module-header\">\n    <i class=\"layui-icon layui-icon-fire\" style=\"color: #ff5722; margin-right: 8px;\"></i>\n    热门新闻</div>\n<div class=\"module-body\">\n        <div class=\"ad-container\">\n        <i class=\"layui-icon layui-icon-file\" style=\"font-size: 48px; color: #ddd;\"></i>\n        <p>暂无热门新闻</p>\n        <p style=\"font-size: 12px; margin-top: 10px;\">请在后台发布新闻文章</p>\n    </div>\n    </div>\n\n                                </div>\n                        <div class=\"module-container module-one-column\">\n                                        <!-- 分类导航模块 -->\n<div class=\"module-header\">\n    <i class=\"layui-icon layui-icon-app\" style=\"color: #1890ff; margin-right: 8px;\"></i>\n    分类导航</div>\n<div class=\"module-body\">\n        <div class=\"category-nav\">\n                <a href=\"/category/1\" class=\"category-item\">\n                        时政新闻        </a>\n                <a href=\"/category/2\" class=\"category-item\">\n                        科技资讯        </a>\n                <a href=\"/category/3\" class=\"category-item\">\n                        体育新闻        </a>\n                <a href=\"/category/4\" class=\"category-item\">\n                        娱乐八卦        </a>\n                <a href=\"/category/5\" class=\"category-item\">\n                        财经资讯        </a>\n                <a href=\"/category/6\" class=\"category-item\">\n                        社会新闻        </a>\n            </div>\n    </div>\n\n                                </div>\n                        <div class=\"module-container module-two-column\">\n                                        <!-- 最新文章模块 -->\n<div class=\"module-header\">\n    <i class=\"layui-icon layui-icon-release\" style=\"color: #52c41a; margin-right: 8px;\"></i>\n    最新文章</div>\n<div class=\"module-body\">\n        <div class=\"ad-container\">\n        <i class=\"layui-icon layui-icon-file\" style=\"font-size: 48px; color: #ddd;\"></i>\n        <p>暂无最新文章</p>\n        <p style=\"font-size: 12px; margin-top: 10px;\">请在后台发布新闻文章</p>\n    </div>\n    </div>\n\n                                </div>\n                        <div class=\"module-container module-one-column\">\n                                        <!-- 侧边广告模块 -->\n<div class=\"module-header\">\n    <i class=\"layui-icon layui-icon-notice\" style=\"color: #fa8c16; margin-right: 8px;\"></i>\n    侧边广告</div>\n<div class=\"module-body\">\n    <div class=\"ad-container\" style=\"min-height: 250px; display: flex; flex-direction: column; justify-content: center;\">\n        <i class=\"layui-icon layui-icon-picture\" style=\"font-size: 48px; color: #ddd; margin-bottom: 15px;\"></i>\n        <p style=\"font-size: 16px; margin-bottom: 10px;\">广告位招租</p>\n        <p style=\"font-size: 12px; color: #999;\">\n            尺寸：300px × 250px        </p>\n        <p style=\"font-size: 12px; color: #999; margin-top: 10px;\">\n            联系电话：400-123-4567\n        </p>\n    </div>\n</div>\n\n                                </div>\n                        <div class=\"module-container module-one-column\">\n                                        <div class=\"module-header\">底部链接</div>\n                        <div class=\"module-body\">\n                            <p>模块 \"footer_links\" 暂未实现</p>\n                        </div>\n                            </div>\n                    </div>\n    </main>\n\n    <!-- 外链模块 -->\n    <section class=\"external-links\">\n        <div class=\"layui-container\">\n            <div class=\"links-header\">\n                <h3>相关链接</h3>\n            </div>\n            <div class=\"links-content\">\n                <div class=\"links-category\">\n                    <div class=\"category-title\">新闻媒体</div>\n                    <div class=\"links-list\">\n                        <a href=\"https://www.xinhuanet.com\" target=\"_blank\" class=\"external-link\">新华网</a>\n                        <a href=\"https://www.people.com.cn\" target=\"_blank\" class=\"external-link\">人民网</a>\n                        <a href=\"https://www.cctv.com\" target=\"_blank\" class=\"external-link\">央视网</a>\n                        <a href=\"https://www.chinanews.com.cn\" target=\"_blank\" class=\"external-link\">中新网</a>\n                        <a href=\"https://www.gmw.cn\" target=\"_blank\" class=\"external-link\">光明网</a>\n                        <a href=\"https://www.chinadaily.com.cn\" target=\"_blank\" class=\"external-link\">中国日报</a>\n                    </div>\n                </div>\n\n                <div class=\"links-category\">\n                    <div class=\"category-title\">科技资讯</div>\n                    <div class=\"links-list\">\n                        <a href=\"https://www.36kr.com\" target=\"_blank\" class=\"external-link\">36氪</a>\n                        <a href=\"https://www.ithome.com\" target=\"_blank\" class=\"external-link\">IT之家</a>\n                        <a href=\"https://www.cnbeta.com\" target=\"_blank\" class=\"external-link\">cnBeta</a>\n                        <a href=\"https://www.pingwest.com\" target=\"_blank\" class=\"external-link\">PingWest</a>\n                        <a href=\"https://www.geekpark.net\" target=\"_blank\" class=\"external-link\">极客公园</a>\n                        <a href=\"https://www.leiphone.com\" target=\"_blank\" class=\"external-link\">雷锋网</a>\n                    </div>\n                </div>\n\n                <div class=\"links-category\">\n                    <div class=\"category-title\">财经金融</div>\n                    <div class=\"links-list\">\n                        <a href=\"https://www.caixin.com\" target=\"_blank\" class=\"external-link\">财新网</a>\n                        <a href=\"https://www.yicai.com\" target=\"_blank\" class=\"external-link\">第一财经</a>\n                        <a href=\"https://www.jiemian.com\" target=\"_blank\" class=\"external-link\">界面新闻</a>\n                        <a href=\"https://www.wallstreetcn.com\" target=\"_blank\" class=\"external-link\">华尔街见闻</a>\n                        <a href=\"https://www.eastmoney.com\" target=\"_blank\" class=\"external-link\">东方财富</a>\n                        <a href=\"https://www.jrj.com.cn\" target=\"_blank\" class=\"external-link\">金融界</a>\n                    </div>\n                </div>\n\n                <div class=\"links-category\">\n                    <div class=\"category-title\">开发者工具</div>\n                    <div class=\"links-list\">\n                        <a href=\"https://github.com\" target=\"_blank\" class=\"external-link\">GitHub</a>\n                        <a href=\"https://gitee.com\" target=\"_blank\" class=\"external-link\">Gitee</a>\n                        <a href=\"https://stackoverflow.com\" target=\"_blank\" class=\"external-link\">Stack Overflow</a>\n                        <a href=\"https://www.runoob.com\" target=\"_blank\" class=\"external-link\">菜鸟教程</a>\n                        <a href=\"https://developer.mozilla.org\" target=\"_blank\" class=\"external-link\">MDN</a>\n                        <a href=\"https://www.w3school.com.cn\" target=\"_blank\" class=\"external-link\">W3School</a>\n                    </div>\n                </div>\n\n                <div class=\"links-category\">\n                    <div class=\"category-title\">API服务</div>\n                    <div class=\"links-list\">\n                        <a href=\"/api/portal/modules/enabled\" target=\"_blank\" class=\"external-link\">门户模块API</a>\n                        <a href=\"/api/admin/portal/configs/groups\" target=\"_blank\" class=\"external-link\">配置分组API</a>\n                        <a href=\"/api/admin/portal/configs/\" target=\"_blank\" class=\"external-link\">配置列表API</a>\n                        <a href=\"/api/admin/portal/modules/\" target=\"_blank\" class=\"external-link\">模块管理API</a>\n                        <a href=\"/admin/portal_config.html\" target=\"_blank\" class=\"external-link\">配置管理后台</a>\n                        <a href=\"/admin/portal_module.html\" target=\"_blank\" class=\"external-link\">模块管理后台</a>\n                    </div>\n                </div>\n\n                <div class=\"links-category\">\n                    <div class=\"category-title\">实用工具</div>\n                    <div class=\"links-list\">\n                        <a href=\"https://www.baidu.com\" target=\"_blank\" class=\"external-link\">百度搜索</a>\n                        <a href=\"https://translate.google.com\" target=\"_blank\" class=\"external-link\">谷歌翻译</a>\n                        <a href=\"https://www.tianqi.com\" target=\"_blank\" class=\"external-link\">天气预报</a>\n                        <a href=\"https://map.baidu.com\" target=\"_blank\" class=\"external-link\">百度地图</a>\n                        <a href=\"https://www.12306.cn\" target=\"_blank\" class=\"external-link\">12306</a>\n                        <a href=\"https://www.sf-express.com\" target=\"_blank\" class=\"external-link\">顺丰速运</a>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </section>\n\n    <!-- 底部版权 -->\n    <footer class=\"footer\">\n        <div class=\"layui-container\">\n            <div class=\"footer-bottom\">\n                <p>&copy; 2025 新闻门户系统. 版权所有 |\n                   <a href=\"#\" style=\"color: #999;\">使用协议</a> |\n                   <a href=\"#\" style=\"color: #999;\">隐私政策</a> |\n                   <a href=\"#\" style=\"color: #999;\">联系我们</a>\n                </p>\n            </div>\n        </div>\n    </footer>\n\n    <script src=\"/static/layui/layui.js\"></script>\n    <script>\n        layui.use(['carousel', 'element'], function(){\n            var carousel = layui.carousel;\n            var element = layui.element;\n\n            // 初始化轮播\n            if (document.getElementById('newsBanner')) {\n                carousel.render({\n                    elem: '#newsBanner',\n                    width: '100%',\n                    height: '400px',\n                    interval: 5000,\n                    anim: 'fade'\n                });\n            }\n        });\n\n        // 更新时间\n        function updateTime() {\n            const now = new Date();\n            const timeStr = now.getFullYear() + '年' +\n                          (now.getMonth() + 1).toString().padStart(2, '0') + '月' +\n                          now.getDate().toString().padStart(2, '0') + '日 ' +\n                          now.getHours().toString().padStart(2, '0') + ':' +\n                          now.getMinutes().toString().padStart(2, '0');\n            document.getElementById('currentTime').textContent = timeStr;\n        }\n\n        // 搜索功能\n        function searchNews() {\n            const keyword = document.getElementById('searchInput').value.trim();\n            if (keyword) {\n                window.location.href = '/search?q=' + encodeURIComponent(keyword);\n            }\n        }\n\n        // 回车搜索\n        document.getElementById('searchInput').addEventListener('keypress', function(e) {\n            if (e.key === 'Enter') {\n                searchNews();\n            }\n        });\n\n        // 初始化\n        updateTime();\n        setInterval(updateTime, 60000); // 每分钟更新一次时间\n    </script>\n</body>\n</html>\n", "response_preview": "\"<!DOCTYPE html>\\n<html lang=\\\"zh-CN\\\">\\n<head>\\n    <meta charset=\\\"UTF-8\\\">\\n    <meta name=\\\"viewport\\\" content=\\\"width=device-width, initial-scale=1.0\\\">\\n    <title>新闻门户系统<\\/title>\\n ..."}, {"url": "http://news.test.jijiaox.com/api/public-portal/modules/enabled", "http_code": 200, "content_type": "application/json; charset=utf-8", "response_length": 24839, "is_actually_json": true, "is_actually_html": true, "json_data": "<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>新闻门户系统</title>\n    <meta name=\"keywords\" content=\"新闻,资讯,门户\">\n    <meta name=\"description\" content=\"专业的新闻资讯门户网站\">\n    <link rel=\"stylesheet\" href=\"/static/layui/css/layui.css\">\n    <style>\n        * { margin: 0; padding: 0; box-sizing: border-box; }\n        body { font-family: 'Microsoft YaHei', Arial, sans-serif; background-color: #f5f5f5; }\n\n        /* 头部样式 */\n        .header {\n            background: #fff;\n            box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n            position: sticky;\n            top: 0;\n            z-index: 1000;\n        }\n        .header-top {\n            background: #1890ff;\n            color: #fff;\n            padding: 8px 0;\n            font-size: 12px;\n        }\n        .header-main {\n            padding: 15px 0;\n        }\n        .logo {\n            font-size: 28px;\n            font-weight: bold;\n            color: #1890ff;\n            text-decoration: none;\n        }\n        .search-box {\n            position: relative;\n        }\n        .search-input {\n            width: 300px;\n            height: 40px;\n            border: 2px solid #1890ff;\n            border-radius: 20px;\n            padding: 0 50px 0 20px;\n            outline: none;\n        }\n        .search-btn {\n            position: absolute;\n            right: 5px;\n            top: 5px;\n            width: 30px;\n            height: 30px;\n            background: #1890ff;\n            border: none;\n            border-radius: 15px;\n            color: #fff;\n            cursor: pointer;\n        }\n\n        /* 导航样式 */\n        .nav-menu {\n            background: #fff;\n            border-top: 1px solid #e6e6e6;\n            padding: 0;\n        }\n        .nav-item {\n            display: inline-block;\n            padding: 15px 25px;\n            color: #333;\n            text-decoration: none;\n            transition: all 0.3s;\n            border-bottom: 3px solid transparent;\n        }\n        .nav-item:hover, .nav-item.active {\n            color: #1890ff;\n            border-bottom-color: #1890ff;\n        }\n\n        /* 主体内容 */\n        .main-content {\n            padding: 20px 0;\n        }\n        .module-container {\n            margin-bottom: 30px;\n            background: #fff;\n            border-radius: 8px;\n            overflow: hidden;\n            box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n        }\n        .module-header {\n            background: #fafafa;\n            padding: 15px 20px;\n            border-bottom: 1px solid #e6e6e6;\n            font-size: 18px;\n            font-weight: bold;\n            color: #333;\n        }\n        .module-body {\n            padding: 20px;\n        }\n\n        /* 轮播样式 */\n        .banner-item {\n            position: relative;\n            height: 400px;\n            background-size: cover;\n            background-position: center;\n            display: flex;\n            align-items: flex-end;\n        }\n        .banner-content {\n            background: linear-gradient(transparent, rgba(0,0,0,0.7));\n            color: #fff;\n            padding: 30px;\n            width: 100%;\n        }\n        .banner-title {\n            font-size: 24px;\n            font-weight: bold;\n            margin-bottom: 10px;\n            line-height: 1.4;\n        }\n        .banner-summary {\n            font-size: 14px;\n            opacity: 0.9;\n            line-height: 1.6;\n        }\n\n        /* 新闻列表样式 */\n        .news-list {\n            list-style: none;\n        }\n        .news-item {\n            display: flex;\n            padding: 15px 0;\n            border-bottom: 1px solid #f0f0f0;\n            transition: all 0.3s;\n        }\n        .news-item:hover {\n            background: #fafafa;\n            margin: 0 -20px;\n            padding: 15px 20px;\n        }\n        .news-item:last-child {\n            border-bottom: none;\n        }\n        .news-image {\n            width: 120px;\n            height: 80px;\n            background-size: cover;\n            background-position: center;\n            border-radius: 6px;\n            margin-right: 15px;\n            flex-shrink: 0;\n        }\n        .news-content {\n            flex: 1;\n        }\n        .news-title {\n            font-size: 16px;\n            font-weight: bold;\n            color: #333;\n            margin-bottom: 8px;\n            line-height: 1.4;\n            text-decoration: none;\n        }\n        .news-title:hover {\n            color: #1890ff;\n        }\n        .news-meta {\n            font-size: 12px;\n            color: #999;\n            margin-bottom: 8px;\n        }\n        .news-summary {\n            font-size: 14px;\n            color: #666;\n            line-height: 1.5;\n            display: -webkit-box;\n            -webkit-line-clamp: 2;\n            -webkit-box-orient: vertical;\n            overflow: hidden;\n        }\n\n        /* 分类导航样式 */\n        .category-nav {\n            display: flex;\n            flex-wrap: wrap;\n            gap: 15px;\n        }\n        .category-item {\n            padding: 10px 20px;\n            background: #f8f9fa;\n            border-radius: 20px;\n            color: #333;\n            text-decoration: none;\n            transition: all 0.3s;\n            border: 1px solid #e9ecef;\n        }\n        .category-item:hover {\n            background: #1890ff;\n            color: #fff;\n            border-color: #1890ff;\n        }\n\n        /* 广告样式 */\n        .ad-container {\n            text-align: center;\n            padding: 20px;\n            background: #f8f9fa;\n            border: 2px dashed #ddd;\n            border-radius: 8px;\n            color: #999;\n        }\n\n        /* 外链模块样式 */\n        .external-links {\n            background: #f8f9fa;\n            padding: 40px 0;\n            margin-top: 50px;\n            border-top: 1px solid #e6e6e6;\n        }\n        .links-header {\n            text-align: center;\n            margin-bottom: 30px;\n        }\n        .links-header h3 {\n            font-size: 24px;\n            color: #333;\n            margin: 0;\n            font-weight: bold;\n        }\n        .links-content {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n            gap: 30px;\n        }\n        .links-category {\n            background: #fff;\n            border-radius: 8px;\n            padding: 20px;\n            box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n        }\n        .category-title {\n            font-size: 16px;\n            font-weight: bold;\n            color: #333;\n            margin-bottom: 15px;\n            padding-bottom: 10px;\n            border-bottom: 2px solid #1890ff;\n        }\n        .links-list {\n            display: grid;\n            grid-template-columns: repeat(2, 1fr);\n            gap: 10px;\n        }\n        .external-link {\n            display: block;\n            padding: 8px 12px;\n            background: #f8f9fa;\n            color: #333;\n            text-decoration: none;\n            border-radius: 4px;\n            transition: all 0.3s;\n            font-size: 14px;\n            border: 1px solid #e9ecef;\n        }\n        .external-link:hover {\n            background: #1890ff;\n            color: #fff;\n            border-color: #1890ff;\n            transform: translateY(-1px);\n        }\n\n        /* 底部样式 */\n        .footer {\n            background: #333;\n            color: #fff;\n            padding: 20px 0;\n        }\n        .footer-bottom {\n            text-align: center;\n            color: #999;\n            font-size: 12px;\n        }\n        .footer-bottom a {\n            color: #999;\n            text-decoration: none;\n            margin: 0 5px;\n        }\n        .footer-bottom a:hover {\n            color: #1890ff;\n        }\n\n        /* 响应式布局 */\n        .module-one-column {\n            width: 100%;\n        }\n        .module-two-column {\n            width: 48%;\n            display: inline-block;\n            vertical-align: top;\n            margin-right: 2%;\n        }\n        .module-two-column:nth-child(2n) {\n            margin-right: 0;\n        }\n\n        /* 移动端适配 */\n        @media (max-width: 768px) {\n            .module-two-column {\n                width: 100%;\n                margin-right: 0;\n                margin-bottom: 20px;\n            }\n            .search-input {\n                width: 200px;\n            }\n            .banner-title {\n                font-size: 18px;\n            }\n            .news-image {\n                width: 80px;\n                height: 60px;\n            }\n            .links-content {\n                grid-template-columns: 1fr;\n                gap: 20px;\n            }\n            .links-list {\n                grid-template-columns: 1fr;\n            }\n            .external-links {\n                padding: 20px 0;\n            }\n        }\n\n        /* 错误提示样式 */\n        .error-message {\n            background: #fff2f0;\n            border: 1px solid #ffccc7;\n            color: #ff4d4f;\n            padding: 15px;\n            border-radius: 6px;\n            margin: 20px 0;\n        }\n    </style>\n</head>\n<body>\n    <!-- 头部 -->\n    <header class=\"header\">\n        <div class=\"header-top\">\n            <div class=\"layui-container\">\n                <div class=\"layui-row\">\n                    <div class=\"layui-col-md6\">\n                        <span>欢迎访问 新闻门户系统</span>\n                    </div>\n                    <div class=\"layui-col-md6\" style=\"text-align: right;\">\n                        <span id=\"currentTime\"></span>\n                    </div>\n                </div>\n            </div>\n        </div>\n\n        <div class=\"header-main\">\n            <div class=\"layui-container\">\n                <div class=\"layui-row layui-col-space15\">\n                    <div class=\"layui-col-md4\">\n                        <a href=\"/\" class=\"logo\">新闻门户系统</a>\n                    </div>\n                    <div class=\"layui-col-md8\" style=\"text-align: right;\">\n                        <div class=\"search-box\">\n                            <input type=\"text\" class=\"search-input\" placeholder=\"搜索新闻...\" id=\"searchInput\">\n                            <button class=\"search-btn\" onclick=\"searchNews()\">\n                                <i class=\"layui-icon layui-icon-search\"></i>\n                            </button>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n\n        <!-- 导航菜单 -->\n        <nav class=\"nav-menu\">\n            <div class=\"layui-container\">\n                <a href=\"/\" class=\"nav-item active\">首页</a>\n                                <a href=\"/category/1\" class=\"nav-item\">时政新闻</a>\n                                <a href=\"/category/2\" class=\"nav-item\">科技资讯</a>\n                                <a href=\"/category/3\" class=\"nav-item\">体育新闻</a>\n                                <a href=\"/category/4\" class=\"nav-item\">娱乐八卦</a>\n                                <a href=\"/category/5\" class=\"nav-item\">财经资讯</a>\n                                <a href=\"/category/6\" class=\"nav-item\">社会新闻</a>\n                            </div>\n        </nav>\n    </header>\n\n    <!-- 主体内容 -->\n    <main class=\"main-content\">\n        <div class=\"layui-container\">\n            \n            <!-- 动态渲染模块 -->\n                        <div class=\"module-container module-one-column\">\n                                        <!-- 新闻轮播模块 -->\n<div class=\"module-header\">新闻轮播</div>\n<div class=\"module-body\">\n    <div class=\"ad-container\">\n        <i class=\"layui-icon layui-icon-picture\" style=\"font-size: 48px; color: #ddd;\"></i>\n        <p>暂无轮播内容</p>\n        <p style=\"font-size: 12px; margin-top: 10px;\">请在后台添加带有封面图的新闻文章</p>\n    </div>\n</div>\n            </div>\n                        <div class=\"module-container module-two-column\">\n                                        <!-- 热门新闻模块 -->\n<div class=\"module-header\">\n    <i class=\"layui-icon layui-icon-fire\" style=\"color: #ff5722; margin-right: 8px;\"></i>\n    热门新闻</div>\n<div class=\"module-body\">\n        <div class=\"ad-container\">\n        <i class=\"layui-icon layui-icon-file\" style=\"font-size: 48px; color: #ddd;\"></i>\n        <p>暂无热门新闻</p>\n        <p style=\"font-size: 12px; margin-top: 10px;\">请在后台发布新闻文章</p>\n    </div>\n    </div>\n\n                                </div>\n                        <div class=\"module-container module-one-column\">\n                                        <!-- 分类导航模块 -->\n<div class=\"module-header\">\n    <i class=\"layui-icon layui-icon-app\" style=\"color: #1890ff; margin-right: 8px;\"></i>\n    分类导航</div>\n<div class=\"module-body\">\n        <div class=\"category-nav\">\n                <a href=\"/category/1\" class=\"category-item\">\n                        时政新闻        </a>\n                <a href=\"/category/2\" class=\"category-item\">\n                        科技资讯        </a>\n                <a href=\"/category/3\" class=\"category-item\">\n                        体育新闻        </a>\n                <a href=\"/category/4\" class=\"category-item\">\n                        娱乐八卦        </a>\n                <a href=\"/category/5\" class=\"category-item\">\n                        财经资讯        </a>\n                <a href=\"/category/6\" class=\"category-item\">\n                        社会新闻        </a>\n            </div>\n    </div>\n\n                                </div>\n                        <div class=\"module-container module-two-column\">\n                                        <!-- 最新文章模块 -->\n<div class=\"module-header\">\n    <i class=\"layui-icon layui-icon-release\" style=\"color: #52c41a; margin-right: 8px;\"></i>\n    最新文章</div>\n<div class=\"module-body\">\n        <div class=\"ad-container\">\n        <i class=\"layui-icon layui-icon-file\" style=\"font-size: 48px; color: #ddd;\"></i>\n        <p>暂无最新文章</p>\n        <p style=\"font-size: 12px; margin-top: 10px;\">请在后台发布新闻文章</p>\n    </div>\n    </div>\n\n                                </div>\n                        <div class=\"module-container module-one-column\">\n                                        <!-- 侧边广告模块 -->\n<div class=\"module-header\">\n    <i class=\"layui-icon layui-icon-notice\" style=\"color: #fa8c16; margin-right: 8px;\"></i>\n    侧边广告</div>\n<div class=\"module-body\">\n    <div class=\"ad-container\" style=\"min-height: 250px; display: flex; flex-direction: column; justify-content: center;\">\n        <i class=\"layui-icon layui-icon-picture\" style=\"font-size: 48px; color: #ddd; margin-bottom: 15px;\"></i>\n        <p style=\"font-size: 16px; margin-bottom: 10px;\">广告位招租</p>\n        <p style=\"font-size: 12px; color: #999;\">\n            尺寸：300px × 250px        </p>\n        <p style=\"font-size: 12px; color: #999; margin-top: 10px;\">\n            联系电话：400-123-4567\n        </p>\n    </div>\n</div>\n\n                                </div>\n                        <div class=\"module-container module-one-column\">\n                                        <div class=\"module-header\">底部链接</div>\n                        <div class=\"module-body\">\n                            <p>模块 \"footer_links\" 暂未实现</p>\n                        </div>\n                            </div>\n                    </div>\n    </main>\n\n    <!-- 外链模块 -->\n    <section class=\"external-links\">\n        <div class=\"layui-container\">\n            <div class=\"links-header\">\n                <h3>相关链接</h3>\n            </div>\n            <div class=\"links-content\">\n                <div class=\"links-category\">\n                    <div class=\"category-title\">新闻媒体</div>\n                    <div class=\"links-list\">\n                        <a href=\"https://www.xinhuanet.com\" target=\"_blank\" class=\"external-link\">新华网</a>\n                        <a href=\"https://www.people.com.cn\" target=\"_blank\" class=\"external-link\">人民网</a>\n                        <a href=\"https://www.cctv.com\" target=\"_blank\" class=\"external-link\">央视网</a>\n                        <a href=\"https://www.chinanews.com.cn\" target=\"_blank\" class=\"external-link\">中新网</a>\n                        <a href=\"https://www.gmw.cn\" target=\"_blank\" class=\"external-link\">光明网</a>\n                        <a href=\"https://www.chinadaily.com.cn\" target=\"_blank\" class=\"external-link\">中国日报</a>\n                    </div>\n                </div>\n\n                <div class=\"links-category\">\n                    <div class=\"category-title\">科技资讯</div>\n                    <div class=\"links-list\">\n                        <a href=\"https://www.36kr.com\" target=\"_blank\" class=\"external-link\">36氪</a>\n                        <a href=\"https://www.ithome.com\" target=\"_blank\" class=\"external-link\">IT之家</a>\n                        <a href=\"https://www.cnbeta.com\" target=\"_blank\" class=\"external-link\">cnBeta</a>\n                        <a href=\"https://www.pingwest.com\" target=\"_blank\" class=\"external-link\">PingWest</a>\n                        <a href=\"https://www.geekpark.net\" target=\"_blank\" class=\"external-link\">极客公园</a>\n                        <a href=\"https://www.leiphone.com\" target=\"_blank\" class=\"external-link\">雷锋网</a>\n                    </div>\n                </div>\n\n                <div class=\"links-category\">\n                    <div class=\"category-title\">财经金融</div>\n                    <div class=\"links-list\">\n                        <a href=\"https://www.caixin.com\" target=\"_blank\" class=\"external-link\">财新网</a>\n                        <a href=\"https://www.yicai.com\" target=\"_blank\" class=\"external-link\">第一财经</a>\n                        <a href=\"https://www.jiemian.com\" target=\"_blank\" class=\"external-link\">界面新闻</a>\n                        <a href=\"https://www.wallstreetcn.com\" target=\"_blank\" class=\"external-link\">华尔街见闻</a>\n                        <a href=\"https://www.eastmoney.com\" target=\"_blank\" class=\"external-link\">东方财富</a>\n                        <a href=\"https://www.jrj.com.cn\" target=\"_blank\" class=\"external-link\">金融界</a>\n                    </div>\n                </div>\n\n                <div class=\"links-category\">\n                    <div class=\"category-title\">开发者工具</div>\n                    <div class=\"links-list\">\n                        <a href=\"https://github.com\" target=\"_blank\" class=\"external-link\">GitHub</a>\n                        <a href=\"https://gitee.com\" target=\"_blank\" class=\"external-link\">Gitee</a>\n                        <a href=\"https://stackoverflow.com\" target=\"_blank\" class=\"external-link\">Stack Overflow</a>\n                        <a href=\"https://www.runoob.com\" target=\"_blank\" class=\"external-link\">菜鸟教程</a>\n                        <a href=\"https://developer.mozilla.org\" target=\"_blank\" class=\"external-link\">MDN</a>\n                        <a href=\"https://www.w3school.com.cn\" target=\"_blank\" class=\"external-link\">W3School</a>\n                    </div>\n                </div>\n\n                <div class=\"links-category\">\n                    <div class=\"category-title\">API服务</div>\n                    <div class=\"links-list\">\n                        <a href=\"/api/portal/modules/enabled\" target=\"_blank\" class=\"external-link\">门户模块API</a>\n                        <a href=\"/api/admin/portal/configs/groups\" target=\"_blank\" class=\"external-link\">配置分组API</a>\n                        <a href=\"/api/admin/portal/configs/\" target=\"_blank\" class=\"external-link\">配置列表API</a>\n                        <a href=\"/api/admin/portal/modules/\" target=\"_blank\" class=\"external-link\">模块管理API</a>\n                        <a href=\"/admin/portal_config.html\" target=\"_blank\" class=\"external-link\">配置管理后台</a>\n                        <a href=\"/admin/portal_module.html\" target=\"_blank\" class=\"external-link\">模块管理后台</a>\n                    </div>\n                </div>\n\n                <div class=\"links-category\">\n                    <div class=\"category-title\">实用工具</div>\n                    <div class=\"links-list\">\n                        <a href=\"https://www.baidu.com\" target=\"_blank\" class=\"external-link\">百度搜索</a>\n                        <a href=\"https://translate.google.com\" target=\"_blank\" class=\"external-link\">谷歌翻译</a>\n                        <a href=\"https://www.tianqi.com\" target=\"_blank\" class=\"external-link\">天气预报</a>\n                        <a href=\"https://map.baidu.com\" target=\"_blank\" class=\"external-link\">百度地图</a>\n                        <a href=\"https://www.12306.cn\" target=\"_blank\" class=\"external-link\">12306</a>\n                        <a href=\"https://www.sf-express.com\" target=\"_blank\" class=\"external-link\">顺丰速运</a>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </section>\n\n    <!-- 底部版权 -->\n    <footer class=\"footer\">\n        <div class=\"layui-container\">\n            <div class=\"footer-bottom\">\n                <p>&copy; 2025 新闻门户系统. 版权所有 |\n                   <a href=\"#\" style=\"color: #999;\">使用协议</a> |\n                   <a href=\"#\" style=\"color: #999;\">隐私政策</a> |\n                   <a href=\"#\" style=\"color: #999;\">联系我们</a>\n                </p>\n            </div>\n        </div>\n    </footer>\n\n    <script src=\"/static/layui/layui.js\"></script>\n    <script>\n        layui.use(['carousel', 'element'], function(){\n            var carousel = layui.carousel;\n            var element = layui.element;\n\n            // 初始化轮播\n            if (document.getElementById('newsBanner')) {\n                carousel.render({\n                    elem: '#newsBanner',\n                    width: '100%',\n                    height: '400px',\n                    interval: 5000,\n                    anim: 'fade'\n                });\n            }\n        });\n\n        // 更新时间\n        function updateTime() {\n            const now = new Date();\n            const timeStr = now.getFullYear() + '年' +\n                          (now.getMonth() + 1).toString().padStart(2, '0') + '月' +\n                          now.getDate().toString().padStart(2, '0') + '日 ' +\n                          now.getHours().toString().padStart(2, '0') + ':' +\n                          now.getMinutes().toString().padStart(2, '0');\n            document.getElementById('currentTime').textContent = timeStr;\n        }\n\n        // 搜索功能\n        function searchNews() {\n            const keyword = document.getElementById('searchInput').value.trim();\n            if (keyword) {\n                window.location.href = '/search?q=' + encodeURIComponent(keyword);\n            }\n        }\n\n        // 回车搜索\n        document.getElementById('searchInput').addEventListener('keypress', function(e) {\n            if (e.key === 'Enter') {\n                searchNews();\n            }\n        });\n\n        // 初始化\n        updateTime();\n        setInterval(updateTime, 60000); // 每分钟更新一次时间\n    </script>\n</body>\n</html>\n", "response_preview": "\"<!DOCTYPE html>\\n<html lang=\\\"zh-CN\\\">\\n<head>\\n    <meta charset=\\\"UTF-8\\\">\\n    <meta name=\\\"viewport\\\" content=\\\"width=device-width, initial-scale=1.0\\\">\\n    <title>新闻门户系统<\\/title>\\n ..."}]}