<?php
/**
 * 精确API诊断工具
 * 
 * 专门检测API路由是否真正返回JSON而不是HTML
 */

echo "🔍 精确API诊断工具\n";
echo "==================\n\n";

function preciseApiTest($url) {
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            'Accept: application/json'
        ],
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_FOLLOWLOCATION => false
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
    curl_close($ch);
    
    // 检查响应内容
    $isActuallyJson = false;
    $isActuallyHtml = false;
    $jsonData = null;
    
    if ($response) {
        // 尝试解析JSON
        $jsonData = json_decode($response, true);
        $isActuallyJson = (json_last_error() === JSON_ERROR_NONE);
        
        // 检查是否包含HTML标签
        $isActuallyHtml = (strpos($response, '<!DOCTYPE html>') !== false || 
                          strpos($response, '<html') !== false ||
                          strpos($response, '<body') !== false);
    }
    
    return [
        'url' => $url,
        'http_code' => $httpCode,
        'content_type' => $contentType,
        'response_length' => strlen($response),
        'is_actually_json' => $isActuallyJson,
        'is_actually_html' => $isActuallyHtml,
        'json_data' => $jsonData,
        'response_preview' => substr($response, 0, 200) . '...'
    ];
}

// 测试关键API端点
$testUrls = [
    'http://news.test.jijiaox.com/api/',
    'http://news.test.jijiaox.com/api/test',
    'http://news.test.jijiaox.com/api/auth/check',
    'http://news.test.jijiaox.com/api/public-portal/modules/enabled'
];

echo "🧪 开始精确测试...\n\n";

$results = [];
foreach ($testUrls as $url) {
    echo "测试: " . $url . "\n";
    $result = preciseApiTest($url);
    $results[] = $result;
    
    echo "  HTTP状态码: " . $result['http_code'] . "\n";
    echo "  声明内容类型: " . ($result['content_type'] ?: '未知') . "\n";
    echo "  响应长度: " . $result['response_length'] . " 字节\n";
    echo "  实际是JSON: " . ($result['is_actually_json'] ? '✅ 是' : '❌ 否') . "\n";
    echo "  实际是HTML: " . ($result['is_actually_html'] ? '⚠️ 是' : '✅ 否') . "\n";
    
    if ($result['is_actually_json']) {
        echo "  📄 JSON数据: " . json_encode($result['json_data'], JSON_UNESCAPED_UNICODE) . "\n";
    } elseif ($result['is_actually_html']) {
        echo "  🏠 HTML预览: " . $result['response_preview'] . "\n";
    }
    
    // 判断结果
    if ($result['http_code'] == 200) {
        if ($result['is_actually_json']) {
            echo "  ✅ 正常：真正的JSON API响应\n";
        } elseif ($result['is_actually_html']) {
            echo "  ❌ 异常：返回HTML页面而不是JSON（路由问题）\n";
        } else {
            echo "  ⚠️ 异常：未知响应格式\n";
        }
    } elseif ($result['http_code'] == 401) {
        echo "  ✅ 正常：需要认证（符合预期）\n";
    } else {
        echo "  ❌ 异常：HTTP状态码 " . $result['http_code'] . "\n";
    }
    
    echo "\n";
}

// 生成精确诊断报告
echo "📊 精确诊断报告\n";
echo "================\n\n";

$totalTests = count($results);
$realJsonResponses = 0;
$fakeJsonResponses = 0;
$htmlResponses = 0;
$otherResponses = 0;

foreach ($results as $result) {
    if ($result['http_code'] == 200) {
        if ($result['is_actually_json']) {
            $realJsonResponses++;
        } elseif ($result['is_actually_html']) {
            $htmlResponses++;
            $fakeJsonResponses++;
        } else {
            $otherResponses++;
        }
    }
}

echo "总测试数: {$totalTests}\n";
echo "真正JSON响应: {$realJsonResponses}\n";
echo "伪装JSON响应: {$fakeJsonResponses}\n";
echo "HTML响应: {$htmlResponses}\n";
echo "其他响应: {$otherResponses}\n";
echo "真实成功率: " . round(($realJsonResponses / $totalTests) * 100, 2) . "%\n\n";

// 问题分析
echo "🔍 问题分析\n";
echo "============\n\n";

if ($fakeJsonResponses > 0) {
    echo "🚨 发现严重问题：有 {$fakeJsonResponses} 个API端点返回HTML而不是JSON！\n\n";
    echo "这表明：\n";
    echo "1. API路由配置有问题，请求被错误路由到门户首页\n";
    echo "2. 多应用模式可能没有正确配置\n";
    echo "3. URL重写规则可能有问题\n\n";
    
    echo "🔧 建议解决方案：\n";
    echo "1. 检查多应用模式配置\n";
    echo "2. 检查API应用是否正确注册\n";
    echo "3. 检查路由优先级\n";
    echo "4. 检查.htaccess重写规则\n\n";
} else {
    echo "✅ 所有API端点都正确返回JSON响应！\n";
}

// 保存详细结果
$report = [
    'test_time' => date('Y-m-d H:i:s'),
    'summary' => [
        'total_tests' => $totalTests,
        'real_json_responses' => $realJsonResponses,
        'fake_json_responses' => $fakeJsonResponses,
        'html_responses' => $htmlResponses,
        'other_responses' => $otherResponses,
        'real_success_rate' => round(($realJsonResponses / $totalTests) * 100, 2)
    ],
    'detailed_results' => $results
];

file_put_contents('precise_api_diagnostic_report.json', json_encode($report, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
echo "📋 详细报告已保存到: precise_api_diagnostic_report.json\n";

echo "\n✅ 精确诊断完成！\n";
