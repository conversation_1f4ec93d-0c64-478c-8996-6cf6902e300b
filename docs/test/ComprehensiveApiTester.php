<?php
declare(strict_types=1);

/**
 * 全面的API接口测试器
 * 
 * 自动测试所有API接口并提供修复建议
 */
class ComprehensiveApiTester
{
    private string $baseUrl;
    private string $token = '';
    private array $testResults = [];
    private array $createdResources = [];
    private bool $autoFix = false;
    
    // 测试统计
    private int $totalTests = 0;
    private int $passedTests = 0;
    private int $failedTests = 0;
    
    public function __construct(string $baseUrl = 'http://localhost', bool $autoFix = false)
    {
        $this->baseUrl = rtrim($baseUrl, '/');
        $this->autoFix = $autoFix;
    }
    
    /**
     * 运行所有API测试
     */
    public function runAllTests(): void
    {
        echo "=== 🚀 全面API接口测试开始 ===\n";
        echo "测试地址: {$this->baseUrl}\n";
        echo "自动修复: " . ($this->autoFix ? '启用' : '禁用') . "\n\n";
        
        try {
            // 1. 基础连接测试
            $this->testBasicConnection();
            
            // 2. 认证相关测试
            $this->testAuthenticationFlow();
            
            // 3. 用户管理测试
            $this->testUserManagement();
            
            // 4. 分类管理测试
            $this->testCategoryManagement();
            
            // 5. 文章管理测试
            $this->testArticleManagement();
            
            // 6. 自定义字段测试
            $this->testCustomFieldManagement();
            
            // 7. 门户配置测试
            $this->testPortalConfiguration();
            
            // 8. 门户模块测试
            $this->testPortalModules();
            
            // 9. 公开接口测试
            $this->testPublicInterfaces();
            
            // 10. 清理测试数据
            $this->cleanupTestData();
            
            // 显示测试结果
            $this->displayTestResults();
            
        } catch (\Exception $e) {
            echo "\n❌ 测试过程中发生严重错误：" . $e->getMessage() . "\n";
            $this->displayTestResults();
        }
    }
    
    /**
     * 基础连接测试
     */
    private function testBasicConnection(): void
    {
        echo "📡 测试基础连接...\n";
        
        // 测试API根路径
        $response = $this->makeRequest('GET', '/api/');
        $this->addTestResult('API根路径访问', $response !== false,
            $response ? '连接成功' : '连接失败');

        // 测试API应用路由
        $response = $this->makeRequest('GET', '/api/test');
        $this->addTestResult('API测试路由', $response !== false,
            $response ? '测试路由正常' : '测试路由异常');
    }
    
    /**
     * 认证流程测试
     */
    private function testAuthenticationFlow(): void
    {
        echo "🔐 测试认证流程...\n";
        
        // 测试登录状态检查（未登录）
        $response = $this->makeRequest('GET', '/api/auth/check');
        $this->addTestResult('未登录状态检查', 
            $response && isset($response['code']) && $response['code'] == 401,
            '正确返回未登录状态');
        
        // 测试登录（使用默认管理员账号）
        $loginData = [
            'username' => 'admin',
            'password' => 'admin123'
        ];
        
        $response = $this->makeRequest('POST', '/api/auth/login', $loginData);
        if ($response && isset($response['code']) && $response['code'] == 200) {
            $this->token = $response['data']['token'] ?? '';
            $this->addTestResult('管理员登录', true, '登录成功，获取到Token');
        } else {
            $this->addTestResult('管理员登录', false, '登录失败：' . ($response['message'] ?? '未知错误'));
            
            // 如果启用自动修复，尝试创建默认管理员
            if ($this->autoFix) {
                $this->createDefaultAdmin();
            }
        }
        
        // 测试登录状态检查（已登录）
        if ($this->token) {
            $response = $this->makeRequest('GET', '/api/auth/check');
            $this->addTestResult('已登录状态检查', 
                $response && isset($response['code']) && $response['code'] == 200,
                '正确返回已登录状态');
            
            // 测试获取用户信息
            $response = $this->makeRequest('GET', '/api/auth/info');
            $this->addTestResult('获取用户信息', 
                $response && isset($response['code']) && $response['code'] == 200,
                '成功获取用户信息');
        }
    }
    
    /**
     * 用户管理测试
     */
    private function testUserManagement(): void
    {
        echo "👥 测试用户管理...\n";
        
        if (!$this->token) {
            $this->addTestResult('用户管理测试', false, '需要先登录');
            return;
        }
        
        // 测试获取用户列表
        $response = $this->makeRequest('GET', '/api/users?page=1&limit=10');
        $this->addTestResult('获取用户列表', 
            $response && isset($response['code']) && $response['code'] == 200,
            '成功获取用户列表');
        
        // 测试创建用户
        $userData = [
            'username' => 'test_user_' . time(),
            'password' => 'test123456',
            'email' => 'test' . time() . '@example.com',
            'real_name' => '测试用户',
            'role' => 'content_admin',
            'status' => 1
        ];
        
        $response = $this->makeRequest('POST', '/api/users', $userData);
        if ($response && isset($response['code']) && $response['code'] == 200) {
            $userId = $response['data']['id'];
            $this->createdResources['users'][] = $userId;
            $this->addTestResult('创建用户', true, '成功创建用户，ID: ' . $userId);
            
            // 测试获取用户详情
            $response = $this->makeRequest('GET', "/api/users/{$userId}");
            $this->addTestResult('获取用户详情', 
                $response && isset($response['code']) && $response['code'] == 200,
                '成功获取用户详情');
            
            // 测试更新用户
            $updateData = ['real_name' => '更新后的测试用户'];
            $response = $this->makeRequest('PUT', "/api/users/{$userId}", $updateData);
            $this->addTestResult('更新用户', 
                $response && isset($response['code']) && $response['code'] == 200,
                '成功更新用户信息');
            
            // 测试修改密码
            $passwordData = ['new_password' => 'newpassword123'];
            $response = $this->makeRequest('POST', "/api/users/{$userId}/password", $passwordData);
            $this->addTestResult('修改用户密码', 
                $response && isset($response['code']) && $response['code'] == 200,
                '成功修改用户密码');
                
        } else {
            $this->addTestResult('创建用户', false, '创建用户失败：' . ($response['message'] ?? '未知错误'));
        }
    }
    
    /**
     * 分类管理测试
     */
    private function testCategoryManagement(): void
    {
        echo "📂 测试分类管理...\n";
        
        if (!$this->token) {
            $this->addTestResult('分类管理测试', false, '需要先登录');
            return;
        }
        
        // 测试获取分类列表
        $response = $this->makeRequest('GET', '/api/categories?page=1&limit=10');
        $this->addTestResult('获取分类列表', 
            $response && isset($response['code']) && $response['code'] == 200,
            '成功获取分类列表');
        
        // 测试获取分类树
        $response = $this->makeRequest('GET', '/api/categories/tree');
        $this->addTestResult('获取分类树', 
            $response && isset($response['code']) && $response['code'] == 200,
            '成功获取分类树');
        
        // 测试创建分类
        $categoryData = [
            'name' => '测试分类_' . time(),
            'slug' => 'test-category-' . time(),
            'description' => '这是一个测试分类',
            'type' => 'list',
            'parent_id' => 0,
            'sort_order' => 100,
            'is_show' => 1
        ];
        
        $response = $this->makeRequest('POST', '/api/categories', $categoryData);
        if ($response && isset($response['code']) && $response['code'] == 200) {
            $categoryId = $response['data']['id'];
            $this->createdResources['categories'][] = $categoryId;
            $this->addTestResult('创建分类', true, '成功创建分类，ID: ' . $categoryId);
            
            // 测试获取分类详情
            $response = $this->makeRequest('GET', "/api/categories/{$categoryId}");
            $this->addTestResult('获取分类详情', 
                $response && isset($response['code']) && $response['code'] == 200,
                '成功获取分类详情');
            
            // 测试更新分类
            $updateData = ['description' => '更新后的分类描述'];
            $response = $this->makeRequest('PUT', "/api/categories/{$categoryId}", $updateData);
            $this->addTestResult('更新分类', 
                $response && isset($response['code']) && $response['code'] == 200,
                '成功更新分类');
            
            // 测试切换显示状态
            $response = $this->makeRequest('POST', "/api/categories/{$categoryId}/toggle-show");
            $this->addTestResult('切换分类显示状态', 
                $response && isset($response['code']) && $response['code'] == 200,
                '成功切换分类显示状态');
                
        } else {
            $this->addTestResult('创建分类', false, '创建分类失败：' . ($response['message'] ?? '未知错误'));
        }
    }
    
    /**
     * HTTP请求方法
     */
    private function makeRequest(string $method, string $path, array $data = []): array|false
    {
        $url = $this->baseUrl . $path;
        $headers = ['Content-Type: application/json'];
        
        if ($this->token) {
            $headers[] = 'Authorization: Bearer ' . $this->token;
        }
        
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_FOLLOWLOCATION => true
        ]);
        
        switch (strtoupper($method)) {
            case 'POST':
                curl_setopt($ch, CURLOPT_POST, true);
                if (!empty($data)) {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
                }
                break;
            case 'PUT':
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
                if (!empty($data)) {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
                }
                break;
            case 'DELETE':
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
                break;
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($response === false) {
            return false;
        }
        
        $decoded = json_decode($response, true);
        return $decoded ?: false;
    }
    
    /**
     * 文章管理测试
     */
    private function testArticleManagement(): void
    {
        echo "📝 测试文章管理...\n";

        if (!$this->token) {
            $this->addTestResult('文章管理测试', false, '需要先登录');
            return;
        }

        // 测试获取文章列表
        $response = $this->makeRequest('GET', '/api/articles?page=1&limit=10');
        $this->addTestResult('获取文章列表',
            $response && isset($response['code']) && $response['code'] == 200,
            '成功获取文章列表');

        // 测试获取文章统计
        $response = $this->makeRequest('GET', '/api/articles/statistics');
        $this->addTestResult('获取文章统计',
            $response && isset($response['code']) && $response['code'] == 200,
            '成功获取文章统计');

        // 获取一个分类ID用于创建文章
        $categoryId = 1; // 默认分类ID
        if (!empty($this->createdResources['categories'])) {
            $categoryId = $this->createdResources['categories'][0];
        }

        // 测试创建文章
        $articleData = [
            'category_id' => $categoryId,
            'title' => '测试文章_' . time(),
            'slug' => 'test-article-' . time(),
            'summary' => '这是一个测试文章的摘要',
            'content' => '<p>这是测试文章的内容</p>',
            'author' => '测试作者',
            'status' => 'published',
            'is_top' => 0,
            'sort_order' => 100
        ];

        $response = $this->makeRequest('POST', '/api/articles', $articleData);
        if ($response && isset($response['code']) && $response['code'] == 200) {
            $articleId = $response['data']['id'];
            $this->createdResources['articles'][] = $articleId;
            $this->addTestResult('创建文章', true, '成功创建文章，ID: ' . $articleId);

            // 测试获取文章详情
            $response = $this->makeRequest('GET', "/api/articles/{$articleId}");
            $this->addTestResult('获取文章详情',
                $response && isset($response['code']) && $response['code'] == 200,
                '成功获取文章详情');

            // 测试更新文章
            $updateData = ['summary' => '更新后的文章摘要'];
            $response = $this->makeRequest('PUT', "/api/articles/{$articleId}", $updateData);
            $this->addTestResult('更新文章',
                $response && isset($response['code']) && $response['code'] == 200,
                '成功更新文章');

            // 测试切换置顶状态
            $response = $this->makeRequest('POST', "/api/articles/{$articleId}/toggle-top");
            $this->addTestResult('切换文章置顶状态',
                $response && isset($response['code']) && $response['code'] == 200,
                '成功切换文章置顶状态');

            // 测试切换文章状态
            $statusData = ['status' => 'draft'];
            $response = $this->makeRequest('POST', "/api/articles/{$articleId}/change-status", $statusData);
            $this->addTestResult('切换文章状态',
                $response && isset($response['code']) && $response['code'] == 200,
                '成功切换文章状态');

        } else {
            $this->addTestResult('创建文章', false, '创建文章失败：' . ($response['message'] ?? '未知错误'));
        }
    }

    /**
     * 自定义字段管理测试
     */
    private function testCustomFieldManagement(): void
    {
        echo "🔧 测试自定义字段管理...\n";

        if (!$this->token) {
            $this->addTestResult('自定义字段管理测试', false, '需要先登录');
            return;
        }

        // 测试获取字段列表
        $response = $this->makeRequest('GET', '/api/article-custom-fields?page=1&limit=10');
        $this->addTestResult('获取自定义字段列表',
            $response && isset($response['code']) && $response['code'] == 200,
            '成功获取自定义字段列表');

        // 测试获取启用字段
        $response = $this->makeRequest('GET', '/api/article-custom-fields/active');
        $this->addTestResult('获取启用字段',
            $response && isset($response['code']) && $response['code'] == 200,
            '成功获取启用字段');

        // 测试创建自定义字段
        $fieldData = [
            'name' => '测试字段_' . time(),
            'field_key' => 'test_field_' . time(),
            'field_type' => 'text',
            'description' => '这是一个测试字段',
            'is_required' => 0,
            'is_active' => 1,
            'sort_order' => 100
        ];

        $response = $this->makeRequest('POST', '/api/article-custom-fields', $fieldData);
        if ($response && isset($response['code']) && $response['code'] == 200) {
            $fieldId = $response['data']['id'];
            $this->createdResources['custom_fields'][] = $fieldId;
            $this->addTestResult('创建自定义字段', true, '成功创建自定义字段，ID: ' . $fieldId);

            // 测试获取字段详情
            $response = $this->makeRequest('GET', "/api/article-custom-fields/{$fieldId}");
            $this->addTestResult('获取字段详情',
                $response && isset($response['code']) && $response['code'] == 200,
                '成功获取字段详情');

            // 测试更新字段
            $updateData = ['description' => '更新后的字段描述'];
            $response = $this->makeRequest('PUT', "/api/article-custom-fields/{$fieldId}", $updateData);
            $this->addTestResult('更新自定义字段',
                $response && isset($response['code']) && $response['code'] == 200,
                '成功更新自定义字段');

            // 测试切换启用状态
            $response = $this->makeRequest('POST', "/api/article-custom-fields/{$fieldId}/toggle-active");
            $this->addTestResult('切换字段启用状态',
                $response && isset($response['code']) && $response['code'] == 200,
                '成功切换字段启用状态');

        } else {
            $this->addTestResult('创建自定义字段', false, '创建自定义字段失败：' . ($response['message'] ?? '未知错误'));
        }
    }

    /**
     * 添加测试结果
     */
    private function addTestResult(string $testName, bool $passed, string $message): void
    {
        $this->totalTests++;
        if ($passed) {
            $this->passedTests++;
            echo "  ✅ {$testName}: {$message}\n";
        } else {
            $this->failedTests++;
            echo "  ❌ {$testName}: {$message}\n";
        }

        $this->testResults[] = [
            'name' => $testName,
            'passed' => $passed,
            'message' => $message,
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }

    /**
     * 门户配置测试
     */
    private function testPortalConfiguration(): void
    {
        echo "⚙️ 测试门户配置...\n";

        if (!$this->token) {
            $this->addTestResult('门户配置测试', false, '需要先登录');
            return;
        }

        // 测试获取配置列表
        $response = $this->makeRequest('GET', '/api/portal/configs/?page=1&limit=10');
        $this->addTestResult('获取门户配置列表',
            $response && isset($response['code']) && $response['code'] == 200,
            '成功获取门户配置列表');

        // 测试获取配置分组
        $response = $this->makeRequest('GET', '/api/portal/configs/groups');
        $this->addTestResult('获取配置分组',
            $response && isset($response['code']) && $response['code'] == 200,
            '成功获取配置分组');

        // 测试创建配置
        $configData = [
            'config_key' => 'test_config_' . time(),
            'config_value' => 'test_value',
            'config_type' => 'string',
            'group_name' => 'test',
            'description' => '测试配置项'
        ];

        $response = $this->makeRequest('POST', '/api/portal/configs/', $configData);
        if ($response && isset($response['code']) && $response['code'] == 200) {
            $configId = $response['data']['id'];
            $this->createdResources['configs'][] = $configId;
            $this->addTestResult('创建门户配置', true, '成功创建门户配置，ID: ' . $configId);

            // 测试获取配置详情
            $response = $this->makeRequest('GET', "/api/portal/configs/{$configId}");
            $this->addTestResult('获取配置详情',
                $response && isset($response['code']) && $response['code'] == 200,
                '成功获取配置详情');

        } else {
            $this->addTestResult('创建门户配置', false, '创建门户配置失败：' . ($response['message'] ?? '未知错误'));
        }

        // 测试批量设置配置
        $batchConfigs = [
            'configs' => [
                'batch_test_1' => 'value1',
                'batch_test_2' => 'value2'
            ],
            'group' => 'batch_test'
        ];

        $response = $this->makeRequest('POST', '/api/portal/configs/batch', $batchConfigs);
        $this->addTestResult('批量设置配置',
            $response && isset($response['code']) && $response['code'] == 200,
            '成功批量设置配置');
    }

    /**
     * 门户模块测试
     */
    private function testPortalModules(): void
    {
        echo "🧩 测试门户模块...\n";

        if (!$this->token) {
            $this->addTestResult('门户模块测试', false, '需要先登录');
            return;
        }

        // 测试获取模块列表
        $response = $this->makeRequest('GET', '/api/portal/modules/?page=1&limit=10');
        $this->addTestResult('获取门户模块列表',
            $response && isset($response['code']) && $response['code'] == 200,
            '成功获取门户模块列表');

        // 测试创建模块
        $moduleData = [
            'module_name' => 'test_module_' . time(),
            'display_name' => '测试模块',
            'description' => '这是一个测试模块',
            'module_type' => 'content',
            'config' => [
                'test_option' => 'test_value'
            ],
            'is_enabled' => true
        ];

        $response = $this->makeRequest('POST', '/api/portal/modules/', $moduleData);
        if ($response && isset($response['code']) && $response['code'] == 200) {
            $moduleId = $response['data']['id'];
            $this->createdResources['modules'][] = $moduleId;
            $this->addTestResult('创建门户模块', true, '成功创建门户模块，ID: ' . $moduleId);

            // 测试获取模块详情
            $response = $this->makeRequest('GET', "/api/portal/modules/{$moduleId}");
            $this->addTestResult('获取模块详情',
                $response && isset($response['code']) && $response['code'] == 200,
                '成功获取模块详情');

            // 测试切换模块状态
            $response = $this->makeRequest('POST', "/api/portal/modules/{$moduleId}/toggle");
            $this->addTestResult('切换模块状态',
                $response && isset($response['code']) && $response['code'] == 200,
                '成功切换模块状态');

        } else {
            $this->addTestResult('创建门户模块', false, '创建门户模块失败：' . ($response['message'] ?? '未知错误'));
        }
    }

    /**
     * 公开接口测试
     */
    private function testPublicInterfaces(): void
    {
        echo "🌐 测试公开接口...\n";

        // 测试公开门户模块列表（无需认证）
        $response = $this->makeRequest('GET', '/api/public-portal/modules/enabled');
        $this->addTestResult('获取启用的门户模块',
            $response && isset($response['code']) && $response['code'] == 200,
            '成功获取启用的门户模块');

        // 测试公开分类接口（需要认证）
        if ($this->token) {
            $response = $this->makeRequest('GET', '/api/public-categories/public/tree');
            $this->addTestResult('获取公开分类树',
                $response && isset($response['code']) && $response['code'] == 200,
                '成功获取公开分类树');

            $response = $this->makeRequest('GET', '/api/public-categories/public/list');
            $this->addTestResult('获取公开分类列表',
                $response && isset($response['code']) && $response['code'] == 200,
                '成功获取公开分类列表');
        }
    }

    /**
     * 清理测试数据
     */
    private function cleanupTestData(): void
    {
        echo "🧹 清理测试数据...\n";

        if (!$this->token) {
            return;
        }

        // 清理创建的文章
        if (!empty($this->createdResources['articles'])) {
            foreach ($this->createdResources['articles'] as $articleId) {
                $response = $this->makeRequest('DELETE', "/api/articles/{$articleId}");
                $this->addTestResult('清理测试文章',
                    $response && isset($response['code']) && $response['code'] == 200,
                    "删除文章 ID: {$articleId}");
            }
        }

        // 清理创建的自定义字段
        if (!empty($this->createdResources['custom_fields'])) {
            foreach ($this->createdResources['custom_fields'] as $fieldId) {
                $response = $this->makeRequest('DELETE', "/api/article-custom-fields/{$fieldId}");
                $this->addTestResult('清理测试字段',
                    $response && isset($response['code']) && $response['code'] == 200,
                    "删除字段 ID: {$fieldId}");
            }
        }

        // 清理创建的分类
        if (!empty($this->createdResources['categories'])) {
            foreach ($this->createdResources['categories'] as $categoryId) {
                $response = $this->makeRequest('DELETE', "/api/categories/{$categoryId}");
                $this->addTestResult('清理测试分类',
                    $response && isset($response['code']) && $response['code'] == 200,
                    "删除分类 ID: {$categoryId}");
            }
        }

        // 清理创建的用户
        if (!empty($this->createdResources['users'])) {
            foreach ($this->createdResources['users'] as $userId) {
                $response = $this->makeRequest('DELETE', "/api/users/{$userId}");
                $this->addTestResult('清理测试用户',
                    $response && isset($response['code']) && $response['code'] == 200,
                    "删除用户 ID: {$userId}");
            }
        }

        // 清理创建的门户模块
        if (!empty($this->createdResources['modules'])) {
            foreach ($this->createdResources['modules'] as $moduleId) {
                $response = $this->makeRequest('DELETE', "/api/portal/modules/{$moduleId}");
                $this->addTestResult('清理测试模块',
                    $response && isset($response['code']) && $response['code'] == 200,
                    "删除模块 ID: {$moduleId}");
            }
        }

        // 清理创建的配置
        if (!empty($this->createdResources['configs'])) {
            foreach ($this->createdResources['configs'] as $configId) {
                $response = $this->makeRequest('DELETE', "/api/portal/configs/{$configId}");
                $this->addTestResult('清理测试配置',
                    $response && isset($response['code']) && $response['code'] == 200,
                    "删除配置 ID: {$configId}");
            }
        }

        // 登出
        if ($this->token) {
            $response = $this->makeRequest('POST', '/api/auth/logout');
            $this->addTestResult('用户登出',
                $response && isset($response['code']) && $response['code'] == 200,
                '成功登出');
        }
    }

    /**
     * 创建默认管理员（自动修复）
     */
    private function createDefaultAdmin(): void
    {
        echo "🔧 尝试创建默认管理员账号...\n";

        // 这里可以添加创建默认管理员的逻辑
        // 由于需要直接操作数据库，这里只是示例
        $this->addTestResult('自动修复', false, '需要手动创建管理员账号');
    }

    /**
     * 显示测试结果
     */
    private function displayTestResults(): void
    {
        echo "\n" . str_repeat("=", 60) . "\n";
        echo "📊 测试结果统计\n";
        echo str_repeat("=", 60) . "\n";

        $successRate = $this->totalTests > 0 ? round(($this->passedTests / $this->totalTests) * 100, 2) : 0;

        echo "总测试数: {$this->totalTests}\n";
        echo "通过数: {$this->passedTests}\n";
        echo "失败数: {$this->failedTests}\n";
        echo "成功率: {$successRate}%\n\n";

        if ($this->failedTests > 0) {
            echo "❌ 失败的测试:\n";
            foreach ($this->testResults as $result) {
                if (!$result['passed']) {
                    echo "  - {$result['name']}: {$result['message']}\n";
                }
            }
            echo "\n";
        }

        // 生成详细报告
        $this->generateDetailedReport();

        if ($successRate >= 90) {
            echo "🎉 恭喜！API测试通过率达到90%以上，系统运行良好！\n";
        } elseif ($successRate >= 70) {
            echo "⚠️ API测试通过率为{$successRate}%，建议检查失败的接口\n";
        } else {
            echo "🚨 API测试通过率较低({$successRate}%)，需要紧急修复！\n";
        }

        echo "\n详细测试报告已保存到: api_test_report.json\n";
    }

    /**
     * 生成详细测试报告
     */
    private function generateDetailedReport(): void
    {
        $report = [
            'test_summary' => [
                'total_tests' => $this->totalTests,
                'passed_tests' => $this->passedTests,
                'failed_tests' => $this->failedTests,
                'success_rate' => $this->totalTests > 0 ? round(($this->passedTests / $this->totalTests) * 100, 2) : 0,
                'test_time' => date('Y-m-d H:i:s'),
                'base_url' => $this->baseUrl
            ],
            'test_results' => $this->testResults,
            'created_resources' => $this->createdResources,
            'recommendations' => $this->generateRecommendations()
        ];

        file_put_contents('api_test_report.json', json_encode($report, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    }

    /**
     * 生成修复建议
     */
    private function generateRecommendations(): array
    {
        $recommendations = [];

        foreach ($this->testResults as $result) {
            if (!$result['passed']) {
                $testName = $result['name'];
                $message = $result['message'];

                if (strpos($testName, '登录') !== false) {
                    $recommendations[] = "检查管理员账号是否存在，默认用户名: admin, 密码: admin123";
                } elseif (strpos($testName, '连接') !== false) {
                    $recommendations[] = "检查API服务是否正常运行，确认URL配置正确";
                } elseif (strpos($testName, '权限') !== false) {
                    $recommendations[] = "检查用户权限配置和中间件设置";
                } elseif (strpos($testName, '数据库') !== false) {
                    $recommendations[] = "检查数据库连接和表结构是否正确";
                } else {
                    $recommendations[] = "检查 {$testName} 相关的业务逻辑和数据验证";
                }
            }
        }

        return array_unique($recommendations);
    }
}

// 使用示例
if (php_sapi_name() === 'cli') {
    $baseUrl = $argv[1] ?? 'http://localhost';
    $autoFix = isset($argv[2]) && $argv[2] === '--auto-fix';

    $tester = new ComprehensiveApiTester($baseUrl, $autoFix);
    $tester->runAllTests();
}
