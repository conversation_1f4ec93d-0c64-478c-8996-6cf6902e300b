<?php
/**
 * 终极API诊断工具
 * 
 * 深度分析API路由问题并提供解决方案
 */

echo "🔍 终极API诊断工具\n";
echo "==================\n\n";

echo "🎯 问题现象：\n";
echo "- API路由返回HTML页面而不是JSON\n";
echo "- 即使创建了控制器和路由，仍然无法正常工作\n";
echo "- 这表明多应用模式或URL重写有问题\n\n";

// 1. 检查入口文件
echo "📋 检查应用入口文件...\n";

$indexFile = '../../public/index.php';
if (file_exists($indexFile)) {
    echo "  ✅ public/index.php 存在\n";
    
    $indexContent = file_get_contents($indexFile);
    
    // 检查多应用模式
    if (strpos($indexContent, 'auto_multi_app') !== false) {
        echo "  ✅ 入口文件包含多应用模式配置\n";
    } else {
        echo "  ❌ 入口文件缺少多应用模式配置\n";
    }
    
    // 检查路由启用
    if (strpos($indexContent, 'with_route') !== false) {
        echo "  ✅ 入口文件包含路由配置\n";
    } else {
        echo "  ❌ 入口文件缺少路由配置\n";
    }
} else {
    echo "  ❌ public/index.php 不存在\n";
}

// 2. 检查.htaccess文件
echo "\n🌐 检查URL重写配置...\n";

$htaccessFile = '../../public/.htaccess';
if (file_exists($htaccessFile)) {
    echo "  ✅ .htaccess 文件存在\n";
    
    $htaccessContent = file_get_contents($htaccessFile);
    
    if (strpos($htaccessContent, 'RewriteEngine On') !== false) {
        echo "  ✅ URL重写已启用\n";
    } else {
        echo "  ❌ URL重写未启用\n";
    }
    
    if (strpos($htaccessContent, 'index.php') !== false) {
        echo "  ✅ 重写规则包含index.php\n";
    } else {
        echo "  ❌ 重写规则缺少index.php\n";
    }
} else {
    echo "  ❌ .htaccess 文件不存在\n";
}

// 3. 检查应用目录结构
echo "\n📁 检查应用目录结构...\n";

$appDirs = [
    '../../app' => 'app根目录',
    '../../app/index' => 'index应用',
    '../../app/api' => 'api应用',
    '../../app/api/controller' => 'api控制器目录',
    '../../app/api/route' => 'api路由目录'
];

foreach ($appDirs as $dir => $name) {
    if (is_dir($dir)) {
        echo "  ✅ {$name} 存在\n";
    } else {
        echo "  ❌ {$name} 不存在\n";
    }
}

// 4. 直接测试应用访问
echo "\n🧪 直接测试应用访问...\n";

function testDirectAccess($url, $name) {
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_HTTPHEADER => ['Accept: application/json'],
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_FOLLOWLOCATION => false
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "  测试 {$name}: ";
    
    if ($httpCode == 200) {
        if (strpos($response, '<!DOCTYPE html>') !== false) {
            echo "❌ 返回HTML页面\n";
            return false;
        } elseif (strpos($response, '"status"') !== false) {
            echo "✅ 返回JSON响应\n";
            return true;
        } else {
            echo "⚠️ 返回未知格式\n";
            return false;
        }
    } else {
        echo "❌ HTTP {$httpCode}\n";
        return false;
    }
}

// 测试不同的访问方式
$testUrls = [
    'http://news.test.jijiaox.com/index.php/api/' => '通过index.php访问API',
    'http://news.test.jijiaox.com/index.php/api/test' => '通过index.php访问API测试',
    'http://news.test.jijiaox.com/api/' => '直接访问API',
    'http://news.test.jijiaox.com/api/test' => '直接访问API测试'
];

$workingUrls = [];
foreach ($testUrls as $url => $name) {
    if (testDirectAccess($url, $name)) {
        $workingUrls[] = $url;
    }
}

// 5. 生成解决方案
echo "\n🔧 解决方案分析\n";
echo "================\n\n";

if (!empty($workingUrls)) {
    echo "✅ 发现可工作的URL：\n";
    foreach ($workingUrls as $url) {
        echo "  - {$url}\n";
    }
    echo "\n💡 建议：\n";
    echo "1. 多应用模式工作正常，但URL重写可能有问题\n";
    echo "2. 可以通过 /index.php/api/ 访问API\n";
    echo "3. 需要修复URL重写规则以支持 /api/ 直接访问\n";
} else {
    echo "❌ 所有测试都失败\n\n";
    echo "🔧 可能的解决方案：\n";
    echo "1. 检查多应用模式配置\n";
    echo "2. 检查应用入口文件\n";
    echo "3. 检查Web服务器配置\n";
    echo "4. 检查ThinkPHP版本兼容性\n";
}

// 6. 创建修复建议
echo "\n📋 修复建议\n";
echo "============\n\n";

echo "🔧 立即可尝试的修复方案：\n\n";

echo "方案1：修复URL重写规则\n";
echo "在 public/.htaccess 中确保有以下内容：\n";
echo "```\n";
echo "RewriteEngine On\n";
echo "RewriteCond %{REQUEST_FILENAME} !-d\n";
echo "RewriteCond %{REQUEST_FILENAME} !-f\n";
echo "RewriteRule ^(.*)$ index.php [L,E=PATH_INFO:$1]\n";
echo "```\n\n";

echo "方案2：在入口文件中强制启用多应用模式\n";
echo "在 public/index.php 中添加：\n";
echo "```php\n";
echo "// 强制开启多应用模式\n";
echo "\$app = new App();\n";
echo "\$app->config->set([\n";
echo "    'auto_multi_app' => true,\n";
echo "    'with_route' => true\n";
echo "], 'app');\n";
echo "```\n\n";

echo "方案3：使用完整路径访问API\n";
echo "如果上述方案不行，可以通过以下URL访问：\n";
echo "- http://news.test.jijiaox.com/index.php/api/test\n";
echo "- http://news.test.jijiaox.com/index.php/api/auth/check\n\n";

// 7. 保存诊断报告
$report = [
    'diagnostic_time' => date('Y-m-d H:i:s'),
    'problem_analysis' => [
        'symptom' => 'API routes return HTML instead of JSON',
        'root_cause' => 'Multi-app mode or URL rewrite configuration issue',
        'severity' => 'high'
    ],
    'file_checks' => [
        'index_php_exists' => file_exists($indexFile),
        'htaccess_exists' => file_exists($htaccessFile),
        'api_app_exists' => is_dir('../../app/api'),
        'api_controllers_exist' => is_dir('../../app/api/controller')
    ],
    'working_urls' => $workingUrls,
    'recommended_solutions' => [
        '1. Fix URL rewrite rules in .htaccess',
        '2. Force enable multi-app mode in index.php',
        '3. Use full path with index.php for API access'
    ]
];

file_put_contents('ultimate_diagnostic_report.json', json_encode($report, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
echo "📋 详细诊断报告已保存到: ultimate_diagnostic_report.json\n";

echo "\n✅ 终极诊断完成！\n";
echo "\n🎯 下一步行动：\n";
echo "1. 根据上述建议修复配置\n";
echo "2. 重新测试API访问\n";
echo "3. 如果仍有问题，检查Web服务器日志\n";
