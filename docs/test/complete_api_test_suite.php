<?php
/**
 * 完整API测试套件
 * 
 * 测试所有API端点的功能性和响应格式
 */

echo "🚀 完整API测试套件\n";
echo "==================\n\n";

// 测试配置
$baseUrl = 'http://news.test.jijiaox.com';
$apiPrefix = '/index.php/api';

// 测试用例定义
$testCases = [
    // 基础API测试
    [
        'name' => 'API测试端点',
        'url' => $baseUrl . $apiPrefix . '/test',
        'method' => 'GET',
        'expected_status' => 'success',
        'expected_fields' => ['status', 'message', 'data']
    ],
    [
        'name' => 'API健康检查',
        'url' => $baseUrl . $apiPrefix . '/health',
        'method' => 'GET',
        'expected_status' => 'healthy',
        'expected_fields' => ['status', 'message', 'timestamp', 'server_time']
    ],
    [
        'name' => '认证检查端点',
        'url' => $baseUrl . $apiPrefix . '/auth/check',
        'method' => 'GET',
        'expected_status' => 'success',
        'expected_fields' => ['status', 'message', 'data']
    ],
    [
        'name' => '登录端点信息',
        'url' => $baseUrl . $apiPrefix . '/auth/login',
        'method' => 'GET',
        'expected_status' => 'success',
        'expected_fields' => ['status', 'message', 'data']
    ]
];

// 测试函数
function runApiTest($testCase) {
    echo "🧪 测试: " . $testCase['name'] . "\n";
    echo "   URL: " . $testCase['url'] . "\n";
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $testCase['url'],
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            'Accept: application/json'
        ],
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_FOLLOWLOCATION => false
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    $result = [
        'test_name' => $testCase['name'],
        'url' => $testCase['url'],
        'http_code' => $httpCode,
        'success' => false,
        'errors' => [],
        'response_data' => null
    ];
    
    // 检查连接错误
    if ($error) {
        $result['errors'][] = "连接错误: $error";
        echo "   ❌ 连接失败: $error\n\n";
        return $result;
    }
    
    // 检查HTTP状态码
    if ($httpCode !== 200) {
        $result['errors'][] = "HTTP状态码错误: $httpCode";
        echo "   ❌ HTTP状态码: $httpCode\n\n";
        return $result;
    }
    
    // 解析JSON响应
    $jsonData = json_decode($response, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        $result['errors'][] = "JSON解析失败: " . json_last_error_msg();
        echo "   ❌ JSON解析失败\n";
        echo "   响应内容: " . substr($response, 0, 200) . "...\n\n";
        return $result;
    }
    
    $result['response_data'] = $jsonData;
    
    // 检查预期状态
    if (isset($testCase['expected_status'])) {
        $actualStatus = $jsonData['status'] ?? null;
        if ($actualStatus !== $testCase['expected_status']) {
            $result['errors'][] = "状态不匹配: 期望 '{$testCase['expected_status']}', 实际 '$actualStatus'";
        }
    }
    
    // 检查预期字段
    if (isset($testCase['expected_fields'])) {
        foreach ($testCase['expected_fields'] as $field) {
            if (!isset($jsonData[$field])) {
                $result['errors'][] = "缺少字段: $field";
            }
        }
    }
    
    // 判断测试结果
    if (empty($result['errors'])) {
        $result['success'] = true;
        echo "   ✅ 测试通过\n";
        echo "   状态: " . ($jsonData['status'] ?? '未知') . "\n";
        echo "   消息: " . ($jsonData['message'] ?? '无消息') . "\n";
    } else {
        echo "   ❌ 测试失败\n";
        foreach ($result['errors'] as $error) {
            echo "   错误: $error\n";
        }
    }
    
    echo "\n";
    return $result;
}

// 执行所有测试
echo "🎯 开始执行API测试...\n\n";

$results = [];
$totalTests = count($testCases);
$passedTests = 0;

foreach ($testCases as $testCase) {
    $result = runApiTest($testCase);
    $results[] = $result;
    
    if ($result['success']) {
        $passedTests++;
    }
}

// 生成测试报告
echo "📊 测试结果统计\n";
echo "================\n";
echo "总测试数: $totalTests\n";
echo "通过数: $passedTests\n";
echo "失败数: " . ($totalTests - $passedTests) . "\n";
echo "通过率: " . round(($passedTests / $totalTests) * 100, 2) . "%\n\n";

// 详细结果
echo "📋 详细测试结果\n";
echo "================\n";

foreach ($results as $result) {
    $status = $result['success'] ? '✅ 通过' : '❌ 失败';
    echo "- {$result['test_name']}: $status\n";
    
    if (!$result['success']) {
        foreach ($result['errors'] as $error) {
            echo "  错误: $error\n";
        }
    }
}

// 性能测试
echo "\n⚡ 性能测试\n";
echo "============\n";

$performanceTest = $baseUrl . $apiPrefix . '/test';
$times = [];

for ($i = 0; $i < 5; $i++) {
    $start = microtime(true);
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $performanceTest,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_HTTPHEADER => ['Accept: application/json'],
        CURLOPT_SSL_VERIFYPEER => false
    ]);
    
    curl_exec($ch);
    curl_close($ch);
    
    $end = microtime(true);
    $times[] = ($end - $start) * 1000; // 转换为毫秒
}

$avgTime = array_sum($times) / count($times);
$minTime = min($times);
$maxTime = max($times);

echo "平均响应时间: " . round($avgTime, 2) . "ms\n";
echo "最快响应时间: " . round($minTime, 2) . "ms\n";
echo "最慢响应时间: " . round($maxTime, 2) . "ms\n";

// 保存测试报告
$report = [
    'test_time' => date('Y-m-d H:i:s'),
    'summary' => [
        'total_tests' => $totalTests,
        'passed_tests' => $passedTests,
        'failed_tests' => $totalTests - $passedTests,
        'pass_rate' => round(($passedTests / $totalTests) * 100, 2)
    ],
    'performance' => [
        'average_response_time_ms' => round($avgTime, 2),
        'min_response_time_ms' => round($minTime, 2),
        'max_response_time_ms' => round($maxTime, 2)
    ],
    'detailed_results' => $results
];

file_put_contents('complete_api_test_report.json', json_encode($report, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

echo "\n📄 完整测试报告已保存到: complete_api_test_report.json\n";

// 最终状态
if ($passedTests === $totalTests) {
    echo "\n🎉 所有API测试通过！系统运行正常！\n";
    echo "✅ API路由修复成功\n";
    echo "✅ 多应用模式正常工作\n";
    echo "✅ JSON响应格式正确\n";
    echo "✅ 控制器加载正常\n";
} else {
    echo "\n⚠️ 部分测试失败，需要进一步调试\n";
}

echo "\n✅ 完整API测试套件执行完成！\n";
