{"test_time": "2025-07-11 15:30:02", "summary": {"total_tests": 13, "passed_tests": 13, "failed_tests": 0, "pass_rate": 100, "average_response_time": 73.55}, "results": [{"name": "API测试端点", "url": "http://news.test.jijiaox.com/index.php/api/test", "success": true, "response_time": 50.73}, {"name": "API健康检查", "url": "http://news.test.jijiaox.com/index.php/api/health", "success": true, "response_time": 51.11}, {"name": "文章列表", "url": "http://news.test.jijiaox.com/index.php/api/articles", "success": true, "response_time": 76.42}, {"name": "文章列表(带分页)", "url": "http://news.test.jijiaox.com/index.php/api/articles?page=1&limit=5", "success": true, "response_time": 81}, {"name": "热门文章", "url": "http://news.test.jijiaox.com/index.php/api/articles/hot", "success": true, "response_time": 77.69}, {"name": "最新文章", "url": "http://news.test.jijiaox.com/index.php/api/articles/latest", "success": true, "response_time": 76.84}, {"name": "轮播文章", "url": "http://news.test.jijiaox.com/index.php/api/articles/banner", "success": true, "response_time": 75.49}, {"name": "文章搜索", "url": "http://news.test.jijiaox.com/index.php/api/articles/search?keyword=AI", "success": true, "response_time": 75.41}, {"name": "分类列表", "url": "http://news.test.jijiaox.com/index.php/api/categories", "success": true, "response_time": 77.07}, {"name": "分类树形结构", "url": "http://news.test.jijiaox.com/index.php/api/categories/tree", "success": true, "response_time": 73.32}, {"name": "启用的门户模块", "url": "http://news.test.jijiaox.com/index.php/api/public-portal/modules/enabled", "success": true, "response_time": 74.81}, {"name": "站点配置", "url": "http://news.test.jijiaox.com/index.php/api/public-portal/configs/site", "success": true, "response_time": 105.04}, {"name": "首页数据", "url": "http://news.test.jijiaox.com/index.php/api/public-portal/home-data", "success": true, "response_time": 61.25}]}