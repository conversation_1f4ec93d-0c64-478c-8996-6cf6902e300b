<?php
/**
 * API路由诊断工具
 * 
 * 专门用于诊断API路由问题
 */

echo "🔍 API路由诊断工具\n";
echo "==================\n\n";

// 测试不同的API路径
$testUrls = [
    'http://news.test.jijiaox.com/api/',
    'http://news.test.jijiaox.com/api/test',
    'http://news.test.jijiaox.com/api/auth/check',
    'http://news.test.jijiaox.com/api/auth/login',
    'http://news.test.jijiaox.com/api/categories',
    'http://news.test.jijiaox.com/api/users',
    'http://news.test.jijiaox.com/api/portal/configs/groups',
    'http://news.test.jijiaox.com/api/public-portal/modules/enabled'
];

function testApiRoute($url) {
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            'Accept: application/json'
        ],
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_FOLLOWLOCATION => false,
        CURLOPT_HEADER => true
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
    $error = curl_error($ch);
    curl_close($ch);
    
    // 分离头部和内容
    $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
    $headers = substr($response, 0, $headerSize);
    $body = substr($response, $headerSize);
    
    return [
        'url' => $url,
        'http_code' => $httpCode,
        'content_type' => $contentType,
        'headers' => $headers,
        'body' => $body,
        'error' => $error,
        'is_json' => strpos($contentType, 'application/json') !== false,
        'is_html' => strpos($contentType, 'text/html') !== false
    ];
}

echo "📡 开始测试API路由...\n\n";

$results = [];
foreach ($testUrls as $url) {
    echo "测试: " . $url . "\n";
    $result = testApiRoute($url);
    $results[] = $result;
    
    echo "  HTTP状态码: " . $result['http_code'] . "\n";
    echo "  内容类型: " . ($result['content_type'] ?: '未知') . "\n";
    
    if ($result['error']) {
        echo "  ❌ 错误: " . $result['error'] . "\n";
    } elseif ($result['http_code'] == 200) {
        if ($result['is_json']) {
            echo "  ✅ 正常 - 返回JSON响应\n";
            $json = json_decode($result['body'], true);
            if ($json) {
                echo "  📄 响应数据: " . json_encode($json, JSON_UNESCAPED_UNICODE) . "\n";
            }
        } elseif ($result['is_html']) {
            echo "  ⚠️ 异常 - 返回HTML页面（应该是JSON）\n";
            // 检查是否是门户页面
            if (strpos($result['body'], '新闻门户系统') !== false) {
                echo "  🏠 检测到门户首页 - 路由被错误重定向\n";
            }
        } else {
            echo "  ⚠️ 异常 - 未知内容类型\n";
        }
    } elseif ($result['http_code'] == 401) {
        echo "  ✅ 正常 - 需要认证（符合预期）\n";
    } elseif ($result['http_code'] == 404) {
        echo "  ❌ 路由未找到\n";
    } elseif ($result['http_code'] == 500) {
        echo "  ❌ 服务器内部错误\n";
    } else {
        echo "  ⚠️ 其他状态码\n";
    }
    
    echo "\n";
}

// 生成诊断报告
echo "📊 诊断报告\n";
echo "============\n\n";

$totalTests = count($results);
$successfulRoutes = 0;
$htmlResponses = 0;
$jsonResponses = 0;
$errorResponses = 0;

foreach ($results as $result) {
    if ($result['http_code'] == 200 && $result['is_json']) {
        $successfulRoutes++;
        $jsonResponses++;
    } elseif ($result['http_code'] == 200 && $result['is_html']) {
        $htmlResponses++;
    } elseif ($result['http_code'] == 401) {
        $successfulRoutes++; // 401也算正常，因为需要认证
    } else {
        $errorResponses++;
    }
}

echo "总测试数: {$totalTests}\n";
echo "成功路由: {$successfulRoutes}\n";
echo "JSON响应: {$jsonResponses}\n";
echo "HTML响应: {$htmlResponses}\n";
echo "错误响应: {$errorResponses}\n";
echo "成功率: " . round(($successfulRoutes / $totalTests) * 100, 2) . "%\n\n";

// 问题分析
echo "🔍 问题分析\n";
echo "============\n\n";

if ($htmlResponses > 0) {
    echo "❌ 发现问题：有 {$htmlResponses} 个API路由返回了HTML页面而不是JSON\n";
    echo "   这通常表示：\n";
    echo "   1. API路由配置有问题\n";
    echo "   2. 多应用模式配置不正确\n";
    echo "   3. URL重写规则有问题\n";
    echo "   4. 路由优先级问题\n\n";
    
    echo "🔧 建议解决方案：\n";
    echo "   1. 检查 app/api/route/app.php 路由配置\n";
    echo "   2. 检查多应用模式是否正确启用\n";
    echo "   3. 检查 .htaccess 重写规则\n";
    echo "   4. 检查路由中间件配置\n\n";
}

if ($errorResponses > 0) {
    echo "❌ 发现 {$errorResponses} 个错误响应\n";
    echo "   需要检查具体的错误日志\n\n";
}

if ($successfulRoutes == $totalTests) {
    echo "✅ 所有API路由工作正常！\n";
} elseif ($successfulRoutes > 0) {
    echo "⚠️ 部分API路由工作正常，需要修复其他问题\n";
} else {
    echo "🚨 所有API路由都有问题，需要全面检查配置\n";
}

// 保存详细结果
$detailedReport = [
    'test_time' => date('Y-m-d H:i:s'),
    'summary' => [
        'total_tests' => $totalTests,
        'successful_routes' => $successfulRoutes,
        'json_responses' => $jsonResponses,
        'html_responses' => $htmlResponses,
        'error_responses' => $errorResponses,
        'success_rate' => round(($successfulRoutes / $totalTests) * 100, 2)
    ],
    'detailed_results' => $results
];

file_put_contents('api_route_diagnostic_report.json', json_encode($detailedReport, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
echo "\n📋 详细报告已保存到: api_route_diagnostic_report.json\n";

echo "\n✅ 诊断完成！\n";
