<?php
declare(strict_types=1);

/**
 * API自动修复助手
 * 
 * 根据测试结果自动修复常见问题
 */
class AutoFixHelper
{
    private string $projectRoot;
    private array $fixLog = [];
    
    public function __construct(string $projectRoot = '')
    {
        $this->projectRoot = $projectRoot ?: dirname(__DIR__, 2);
    }
    
    /**
     * 根据测试报告执行自动修复
     */
    public function autoFixFromReport(string $reportPath): array
    {
        if (!file_exists($reportPath)) {
            throw new Exception("测试报告文件不存在: {$reportPath}");
        }
        
        $report = json_decode(file_get_contents($reportPath), true);
        if (!$report) {
            throw new Exception("无法解析测试报告");
        }
        
        echo "🔧 开始自动修复...\n";
        
        // 分析失败的测试
        $failedTests = array_filter($report['test_results'], function($test) {
            return !$test['passed'];
        });
        
        foreach ($failedTests as $test) {
            $this->analyzeAndFix($test);
        }
        
        // 执行通用修复
        $this->performCommonFixes();
        
        // 生成修复报告
        $this->generateFixReport();
        
        return $this->fixLog;
    }
    
    /**
     * 分析单个测试失败并尝试修复
     */
    private function analyzeAndFix(array $test): void
    {
        $testName = $test['name'];
        $message = $test['message'];
        
        echo "🔍 分析失败测试: {$testName}\n";
        
        // 连接问题修复
        if (strpos($testName, '连接') !== false || strpos($message, '连接') !== false) {
            $this->fixConnectionIssues();
        }
        
        // 认证问题修复
        if (strpos($testName, '登录') !== false || strpos($message, '401') !== false) {
            $this->fixAuthenticationIssues();
        }
        
        // 权限问题修复
        if (strpos($message, '403') !== false || strpos($message, '权限') !== false) {
            $this->fixPermissionIssues();
        }
        
        // 路由问题修复
        if (strpos($message, '404') !== false || strpos($message, '路由') !== false) {
            $this->fixRoutingIssues();
        }
        
        // 数据库问题修复
        if (strpos($message, '数据库') !== false || strpos($message, 'database') !== false) {
            $this->fixDatabaseIssues();
        }
    }
    
    /**
     * 修复连接问题
     */
    private function fixConnectionIssues(): void
    {
        $this->addFixLog('连接问题检查', '检查Web服务器配置和URL重写规则');
        
        // 检查.htaccess文件
        $htaccessPath = $this->projectRoot . '/public/.htaccess';
        if (!file_exists($htaccessPath)) {
            $htaccessContent = <<<EOT
<IfModule mod_rewrite.c>
    Options -Multiviews
    RewriteEngine On
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^(.*)$ index.php [QSA,PT,L]
</IfModule>
EOT;
            file_put_contents($htaccessPath, $htaccessContent);
            $this->addFixLog('创建.htaccess', '已创建URL重写规则文件');
        }
        
        // 检查入口文件
        $indexPath = $this->projectRoot . '/public/index.php';
        if (!file_exists($indexPath)) {
            $this->addFixLog('入口文件检查', '请确保public/index.php文件存在');
        }
    }
    
    /**
     * 修复认证问题
     */
    private function fixAuthenticationIssues(): void
    {
        $this->addFixLog('认证问题修复', '检查默认管理员账号');
        
        // 生成创建管理员的SQL
        $adminSql = <<<SQL
-- 创建默认管理员账号
INSERT INTO admin_users (username, password, email, real_name, role, status, create_time, update_time) 
VALUES (
    'admin', 
    '$2y$10\$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: admin123
    '<EMAIL>',
    '系统管理员',
    'super_admin',
    1,
    NOW(),
    NOW()
) ON DUPLICATE KEY UPDATE 
    password = VALUES(password),
    role = VALUES(role),
    status = VALUES(status);
SQL;
        
        file_put_contents($this->projectRoot . '/docs/sql/create_admin.sql', $adminSql);
        $this->addFixLog('管理员账号', '已生成创建管理员的SQL文件: docs/sql/create_admin.sql');
    }
    
    /**
     * 修复权限问题
     */
    private function fixPermissionIssues(): void
    {
        $this->addFixLog('权限问题修复', '检查中间件和权限注解配置');
        
        // 检查中间件配置
        $middlewarePath = $this->projectRoot . '/app/api/middleware/AuthMiddleware.php';
        if (file_exists($middlewarePath)) {
            $content = file_get_contents($middlewarePath);
            
            // 检查是否需要更新排除路由
            if (strpos($content, 'api/admin/auth/login') !== false) {
                $content = str_replace('api/admin/auth/login', 'api/auth/login', $content);
                $content = str_replace('api/admin/auth/check', 'api/auth/check', $content);
                file_put_contents($middlewarePath, $content);
                $this->addFixLog('中间件更新', '已更新AuthMiddleware中的排除路由');
            }
        }
    }
    
    /**
     * 修复路由问题
     */
    private function fixRoutingIssues(): void
    {
        $this->addFixLog('路由问题修复', '检查路由配置文件');
        
        // 检查路由文件是否存在
        $routePath = $this->projectRoot . '/app/api/route/app.php';
        if (!file_exists($routePath)) {
            $this->addFixLog('路由文件', '路由配置文件不存在: ' . $routePath);
        }
    }
    
    /**
     * 修复数据库问题
     */
    private function fixDatabaseIssues(): void
    {
        $this->addFixLog('数据库问题修复', '检查数据库连接和表结构');
        
        // 生成数据库检查脚本
        $checkScript = <<<PHP
<?php
// 数据库连接检查脚本
try {
    \$config = include 'config/database.php';
    \$dsn = "mysql:host={\$config['connections']['mysql']['hostname']};dbname={\$config['connections']['mysql']['database']}";
    \$pdo = new PDO(\$dsn, \$config['connections']['mysql']['username'], \$config['connections']['mysql']['password']);
    echo "✅ 数据库连接成功\n";
    
    // 检查必要的表
    \$tables = ['admin_users', 'article_categories', 'articles', 'article_custom_fields'];
    foreach (\$tables as \$table) {
        \$stmt = \$pdo->query("SHOW TABLES LIKE '\$table'");
        if (\$stmt->rowCount() > 0) {
            echo "✅ 表 \$table 存在\n";
        } else {
            echo "❌ 表 \$table 不存在\n";
        }
    }
} catch (Exception \$e) {
    echo "❌ 数据库连接失败: " . \$e->getMessage() . "\n";
}
PHP;
        
        file_put_contents($this->projectRoot . '/docs/test/check_database.php', $checkScript);
        $this->addFixLog('数据库检查', '已生成数据库检查脚本: docs/test/check_database.php');
    }
    
    /**
     * 执行通用修复
     */
    private function performCommonFixes(): void
    {
        echo "🔧 执行通用修复...\n";
        
        // 创建必要的目录
        $dirs = [
            'runtime/cache',
            'runtime/log',
            'runtime/temp',
            'docs/sql',
            'docs/test'
        ];
        
        foreach ($dirs as $dir) {
            $fullPath = $this->projectRoot . '/' . $dir;
            if (!is_dir($fullPath)) {
                mkdir($fullPath, 0755, true);
                $this->addFixLog('目录创建', "已创建目录: {$dir}");
            }
        }
        
        // 设置目录权限
        $writableDirs = ['runtime', 'public/uploads'];
        foreach ($writableDirs as $dir) {
            $fullPath = $this->projectRoot . '/' . $dir;
            if (is_dir($fullPath)) {
                chmod($fullPath, 0755);
                $this->addFixLog('权限设置', "已设置目录权限: {$dir}");
            }
        }
    }
    
    /**
     * 添加修复日志
     */
    private function addFixLog(string $type, string $message): void
    {
        $this->fixLog[] = [
            'type' => $type,
            'message' => $message,
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        echo "  🔧 {$type}: {$message}\n";
    }
    
    /**
     * 生成修复报告
     */
    private function generateFixReport(): void
    {
        $report = [
            'fix_time' => date('Y-m-d H:i:s'),
            'total_fixes' => count($this->fixLog),
            'fixes' => $this->fixLog,
            'next_steps' => [
                '1. 检查数据库连接配置',
                '2. 运行数据库迁移脚本',
                '3. 创建默认管理员账号',
                '4. 重新运行API测试',
                '5. 检查Web服务器配置'
            ]
        ];
        
        $reportPath = $this->projectRoot . '/docs/test/auto_fix_report.json';
        file_put_contents($reportPath, json_encode($report, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        
        echo "\n📋 修复报告已保存到: {$reportPath}\n";
        echo "🎯 总共执行了 " . count($this->fixLog) . " 项修复操作\n";
    }
}

// 命令行使用
if (php_sapi_name() === 'cli') {
    $reportPath = $argv[1] ?? 'docs/test/api_test_report.json';
    
    try {
        $fixer = new AutoFixHelper();
        $fixes = $fixer->autoFixFromReport($reportPath);
        
        echo "\n✅ 自动修复完成！\n";
        echo "请按照修复报告中的后续步骤继续操作。\n";
    } catch (Exception $e) {
        echo "❌ 自动修复失败: " . $e->getMessage() . "\n";
        exit(1);
    }
}
