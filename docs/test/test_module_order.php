<?php
/**
 * 测试模块排序功能
 */

// 引入ThinkPHP框架
require_once __DIR__ . '/../../vendor/autoload.php';

use think\facade\Db;
use app\common\service\PortalModuleService;

try {
    // 初始化应用
    $app = new \think\App();
    $app->initialize();
    
    echo "=== 测试模块排序功能 ===\n\n";
    
    // 1. 直接查询数据库
    echo "1. 数据库中的模块排序（按sort_order ASC）：\n";
    $dbModules = Db::table('portal_modules')
        ->field('id,module_name,module_title,sort_order,is_enabled')
        ->order('sort_order', 'asc')
        ->select();
    
    foreach ($dbModules as $module) {
        $status = $module['is_enabled'] ? '启用' : '禁用';
        echo "  {$module['sort_order']}. {$module['module_title']} ({$module['module_name']}) - {$status}\n";
    }
    
    echo "\n";
    
    // 2. 通过Service获取启用的模块
    echo "2. 通过PortalModuleService获取的启用模块：\n";
    $moduleService = new PortalModuleService();
    $enabledModules = $moduleService->getEnabledModules(false); // 不使用缓存
    
    foreach ($enabledModules as $module) {
        echo "  {$module['sort_order']}. {$module['module_title']} ({$module['module_name']})\n";
    }
    
    echo "\n";
    
    // 3. 检查缓存
    echo "3. 检查缓存中的模块数据：\n";
    $cachedModules = $moduleService->getEnabledModules(true); // 使用缓存
    
    foreach ($cachedModules as $module) {
        echo "  {$module['sort_order']}. {$module['module_title']} ({$module['module_name']})\n";
    }
    
    echo "\n=== 测试完成 ===\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
}
