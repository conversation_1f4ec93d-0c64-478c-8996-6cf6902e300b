{"diagnostic_time": "2025-07-11 11:52:41", "problem_analysis": {"symptom": "API routes return HTML instead of JSON", "root_cause": "Multi-app mode or URL rewrite configuration issue", "severity": "high"}, "file_checks": {"index_php_exists": true, "htaccess_exists": true, "api_app_exists": true, "api_controllers_exist": true}, "working_urls": [], "recommended_solutions": ["1. Fix URL rewrite rules in .htaccess", "2. Force enable multi-app mode in index.php", "3. Use full path with index.php for API access"]}