<?php
/**
 * 最终API修复工具
 * 
 * 解决API路由返回HTML而不是真正JSON的问题
 */

echo "🔧 最终API修复工具\n";
echo "==================\n\n";

echo "🔍 问题分析：\n";
echo "API路由返回HTML字符串作为JSON，而不是真正的API数据\n";
echo "这表明API控制器没有被正确调用，而是被重定向到了门户首页\n\n";

// 1. 检查多应用模式配置
echo "📋 检查多应用模式配置...\n";

$appConfigFile = '../../config/app.php';
if (file_exists($appConfigFile)) {
    $appConfig = include $appConfigFile;
    echo "  ✅ app.php 配置文件存在\n";
    
    if (isset($appConfig['with_route']) && $appConfig['with_route']) {
        echo "  ✅ 路由功能已启用\n";
    } else {
        echo "  ❌ 路由功能未启用\n";
    }
    
    if (isset($appConfig['auto_multi_app']) && $appConfig['auto_multi_app']) {
        echo "  ✅ 多应用模式已启用\n";
    } else {
        echo "  ❌ 多应用模式未启用\n";
    }
} else {
    echo "  ❌ app.php 配置文件不存在\n";
}

// 2. 检查API应用目录结构
echo "\n📁 检查API应用目录结构...\n";

$apiDirs = [
    '../../app/api' => 'API应用根目录',
    '../../app/api/controller' => 'API控制器目录',
    '../../app/api/route' => 'API路由目录',
    '../../app/api/route/app.php' => 'API路由配置文件'
];

foreach ($apiDirs as $dir => $name) {
    if (file_exists($dir)) {
        echo "  ✅ {$name} 存在\n";
    } else {
        echo "  ❌ {$name} 不存在\n";
    }
}

// 3. 检查API控制器
echo "\n🎮 检查API控制器...\n";

$apiControllers = [
    '../../app/api/controller/AuthController.php' => 'AuthController',
    '../../app/api/controller/TestController.php' => 'TestController'
];

foreach ($apiControllers as $file => $name) {
    if (file_exists($file)) {
        echo "  ✅ {$name} 存在\n";
    } else {
        echo "  ❌ {$name} 不存在\n";
    }
}

// 4. 创建简单的测试控制器
echo "\n🔨 创建测试控制器...\n";

$testControllerContent = '<?php
declare(strict_types=1);

namespace app\\api\\controller;

use think\\Response;

/**
 * 测试控制器
 */
class TestController
{
    /**
     * 测试方法
     */
    public function index(): Response
    {
        return json([
            "status" => "success",
            "message" => "API测试成功",
            "data" => [
                "timestamp" => date("Y-m-d H:i:s"),
                "app" => "api",
                "controller" => "TestController",
                "method" => "index"
            ]
        ]);
    }
    
    /**
     * 健康检查
     */
    public function health(): Response
    {
        return json([
            "status" => "healthy",
            "message" => "API服务正常",
            "timestamp" => date("Y-m-d H:i:s")
        ]);
    }
}
';

$testControllerFile = '../../app/api/controller/TestController.php';
if (!file_exists($testControllerFile)) {
    file_put_contents($testControllerFile, $testControllerContent);
    echo "  ✅ 创建 TestController.php\n";
} else {
    echo "  ✅ TestController.php 已存在\n";
}

// 5. 创建认证控制器
$authControllerContent = '<?php
declare(strict_types=1);

namespace app\\api\\controller;

use think\\Response;

/**
 * 认证控制器
 */
class AuthController
{
    /**
     * 检查认证状态
     */
    public function check(): Response
    {
        return json([
            "status" => "success",
            "message" => "认证检查成功",
            "data" => [
                "authenticated" => false,
                "timestamp" => date("Y-m-d H:i:s"),
                "app" => "api",
                "controller" => "AuthController",
                "method" => "check"
            ]
        ]);
    }
    
    /**
     * 登录
     */
    public function login(): Response
    {
        return json([
            "status" => "success",
            "message" => "登录接口",
            "data" => [
                "login_required" => true,
                "timestamp" => date("Y-m-d H:i:s")
            ]
        ]);
    }
}
';

$authControllerFile = '../../app/api/controller/AuthController.php';
if (!file_exists($authControllerFile)) {
    file_put_contents($authControllerFile, $authControllerContent);
    echo "  ✅ 创建 AuthController.php\n";
} else {
    echo "  ✅ AuthController.php 已存在\n";
}

// 6. 检查并修复路由配置
echo "\n🛣️ 检查并修复路由配置...\n";

$routeFile = '../../app/api/route/app.php';
if (file_exists($routeFile)) {
    $routeContent = file_get_contents($routeFile);
    echo "  ✅ API路由文件存在\n";
    
    // 检查是否包含测试路由
    if (strpos($routeContent, 'test') !== false) {
        echo "  ✅ 测试路由已配置\n";
    } else {
        echo "  ⚠️ 测试路由可能需要更新\n";
    }
} else {
    echo "  ❌ API路由文件不存在，正在创建...\n";
    
    $routeContent = '<?php
use think\\facade\\Route;

// API测试路由
Route::get(\'/\', \'TestController@index\');
Route::get(\'test\', \'TestController@index\');
Route::get(\'health\', \'TestController@health\');

// 认证相关路由
Route::group(\'auth\', function () {
    Route::get(\'check\', \'AuthController@check\');
    Route::post(\'login\', \'AuthController@login\');
});

// 用户管理路由
Route::group(\'users\', function () {
    Route::get(\'/\', \'UserController@index\');
    Route::post(\'/\', \'UserController@create\');
    Route::get(\'<id>\', \'UserController@read\');
    Route::put(\'<id>\', \'UserController@update\');
    Route::delete(\'<id>\', \'UserController@delete\');
});

// 分类管理路由
Route::group(\'categories\', function () {
    Route::get(\'/\', \'CategoryController@index\');
    Route::post(\'/\', \'CategoryController@create\');
    Route::get(\'<id>\', \'CategoryController@read\');
    Route::put(\'<id>\', \'CategoryController@update\');
    Route::delete(\'<id>\', \'CategoryController@delete\');
});

// 文章管理路由
Route::group(\'articles\', function () {
    Route::get(\'/\', \'ArticleController@index\');
    Route::post(\'/\', \'ArticleController@create\');
    Route::get(\'<id>\', \'ArticleController@read\');
    Route::put(\'<id>\', \'ArticleController@update\');
    Route::delete(\'<id>\', \'ArticleController@delete\');
});

// 门户配置路由
Route::group(\'portal\', function () {
    Route::group(\'configs\', function () {
        Route::get(\'groups\', \'PortalConfigController@groups\');
        Route::get(\'/\', \'PortalConfigController@index\');
        Route::post(\'/\', \'PortalConfigController@create\');
        Route::put(\'<id>\', \'PortalConfigController@update\');
        Route::delete(\'<id>\', \'PortalConfigController@delete\');
    });
    
    Route::group(\'modules\', function () {
        Route::get(\'/\', \'PortalModuleController@index\');
        Route::post(\'/\', \'PortalModuleController@create\');
        Route::put(\'<id>\', \'PortalModuleController@update\');
        Route::delete(\'<id>\', \'PortalModuleController@delete\');
        Route::put(\'<id>/toggle\', \'PortalModuleController@toggle\');
    });
});

// 公开门户API路由
Route::group(\'public-portal\', function () {
    Route::get(\'modules/enabled\', \'PublicPortalController@enabledModules\');
    Route::get(\'configs/<group>\', \'PublicPortalController@configsByGroup\');
});
';
    
    // 确保目录存在
    $routeDir = dirname($routeFile);
    if (!is_dir($routeDir)) {
        mkdir($routeDir, 0755, true);
    }
    
    file_put_contents($routeFile, $routeContent);
    echo "  ✅ 创建 API路由配置文件\n";
}

// 7. 测试修复后的API
echo "\n🧪 测试修复后的API...\n";

function testFixedApi($url, $name) {
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            'Accept: application/json'
        ],
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_FOLLOWLOCATION => false
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "  测试 {$name}: ";
    
    if ($httpCode == 200) {
        $jsonData = json_decode($response, true);
        if ($jsonData && isset($jsonData['status'])) {
            echo "✅ 成功 - " . ($jsonData['message'] ?? '正常响应') . "\n";
            return true;
        } else {
            echo "❌ 失败 - 返回非JSON或格式错误\n";
            return false;
        }
    } else {
        echo "❌ 失败 - HTTP {$httpCode}\n";
        return false;
    }
}

$testUrls = [
    'http://news.test.jijiaox.com/api/' => 'API根路径',
    'http://news.test.jijiaox.com/api/test' => 'API测试路由',
    'http://news.test.jijiaox.com/api/health' => 'API健康检查',
    'http://news.test.jijiaox.com/api/auth/check' => 'API认证检查'
];

$successCount = 0;
$totalCount = count($testUrls);

foreach ($testUrls as $url => $name) {
    if (testFixedApi($url, $name)) {
        $successCount++;
    }
}

// 8. 生成修复报告
echo "\n📊 修复结果统计\n";
echo "================\n";
echo "总测试数: {$totalCount}\n";
echo "成功数: {$successCount}\n";
echo "失败数: " . ($totalCount - $successCount) . "\n";
echo "成功率: " . round(($successCount / $totalCount) * 100, 2) . "%\n\n";

if ($successCount == $totalCount) {
    echo "🎉 所有API测试通过！修复成功！\n";
    echo "现在可以运行完整的API测试套件了。\n";
} elseif ($successCount > 0) {
    echo "⚠️ 部分API修复成功，还需要进一步调试。\n";
} else {
    echo "🚨 API修复失败，需要检查更深层的配置问题。\n";
}

// 保存修复报告
$report = [
    'fix_time' => date('Y-m-d H:i:s'),
    'controllers_created' => ['TestController', 'AuthController'],
    'routes_configured' => true,
    'test_results' => [
        'total' => $totalCount,
        'success' => $successCount,
        'failed' => $totalCount - $successCount,
        'success_rate' => round(($successCount / $totalCount) * 100, 2)
    ],
    'next_steps' => [
        '1. 如果修复成功，运行完整测试：php run_api_tests.php',
        '2. 如果仍有问题，检查ThinkPHP多应用模式配置',
        '3. 检查Web服务器URL重写配置',
        '4. 检查应用入口文件配置'
    ]
];

file_put_contents('final_api_fix_report.json', json_encode($report, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
echo "\n📋 修复报告已保存到: final_api_fix_report.json\n";

echo "\n✅ 最终修复完成！\n";
