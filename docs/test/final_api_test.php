<?php
/**
 * 最终API测试工具
 * 
 * 使用正确的URL格式测试所有API端点
 */

echo "🎉 最终API测试工具\n";
echo "==================\n\n";

// 测试配置
$baseUrl = 'http://news.test.jijiaox.com';
$apiPrefix = '/index.php/api';

// 最终测试用例 (使用正确的URL格式)
$finalTestCases = [
    // 基础测试
    [
        'name' => 'API测试端点',
        'url' => $baseUrl . $apiPrefix . '/test',
        'method' => 'GET'
    ],
    [
        'name' => 'API健康检查',
        'url' => $baseUrl . $apiPrefix . '/health',
        'method' => 'GET'
    ],
    
    // 文章相关测试 (修正URL)
    [
        'name' => '文章列表',
        'url' => $baseUrl . $apiPrefix . '/articles',
        'method' => 'GET'
    ],
    [
        'name' => '文章列表(带分页)',
        'url' => $baseUrl . $apiPrefix . '/articles?page=1&limit=5',
        'method' => 'GET'
    ],
    [
        'name' => '热门文章',
        'url' => $baseUrl . $apiPrefix . '/articles/hot',
        'method' => 'GET'
    ],
    [
        'name' => '最新文章',
        'url' => $baseUrl . $apiPrefix . '/articles/latest',
        'method' => 'GET'
    ],
    [
        'name' => '轮播文章',
        'url' => $baseUrl . $apiPrefix . '/articles/banner',
        'method' => 'GET'
    ],
    [
        'name' => '文章搜索',
        'url' => $baseUrl . $apiPrefix . '/articles/search?keyword=AI',
        'method' => 'GET'
    ],
    
    // 分类测试 (修正URL)
    [
        'name' => '分类列表',
        'url' => $baseUrl . $apiPrefix . '/categories',
        'method' => 'GET'
    ],
    [
        'name' => '分类树形结构',
        'url' => $baseUrl . $apiPrefix . '/categories/tree',
        'method' => 'GET'
    ],
    
    // 门户测试
    [
        'name' => '启用的门户模块',
        'url' => $baseUrl . $apiPrefix . '/public-portal/modules/enabled',
        'method' => 'GET'
    ],
    [
        'name' => '站点配置',
        'url' => $baseUrl . $apiPrefix . '/public-portal/configs/site',
        'method' => 'GET'
    ],
    [
        'name' => '首页数据',
        'url' => $baseUrl . $apiPrefix . '/public-portal/home-data',
        'method' => 'GET'
    ]
];

// 最终测试函数
function runFinalTest($testCase) {
    echo "🧪 最终测试: " . $testCase['name'] . "\n";
    echo "   URL: " . $testCase['url'] . "\n";
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $testCase['url'],
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 15,
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            'Accept: application/json'
        ],
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_FOLLOWLOCATION => false,
        CURLOPT_VERBOSE => false
    ]);
    
    $startTime = microtime(true);
    $response = curl_exec($ch);
    $endTime = microtime(true);
    
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
    $error = curl_error($ch);
    curl_close($ch);
    
    $responseTime = round(($endTime - $startTime) * 1000, 2);
    
    echo "   响应时间: {$responseTime}ms\n";
    echo "   HTTP状态码: $httpCode\n";
    echo "   内容类型: " . ($contentType ?: '未知') . "\n";
    
    // 检查连接错误
    if ($error) {
        echo "   ❌ 连接错误: $error\n\n";
        return false;
    }
    
    // 检查HTTP状态码
    if ($httpCode !== 200) {
        echo "   ❌ HTTP错误: $httpCode\n";
        if ($response) {
            echo "   错误响应: " . substr($response, 0, 200) . "...\n";
        }
        echo "\n";
        return false;
    }
    
    // 检查响应内容
    if (empty($response)) {
        echo "   ❌ 空响应\n\n";
        return false;
    }
    
    // 尝试解析JSON
    $jsonData = json_decode($response, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        echo "   ❌ JSON解析失败: " . json_last_error_msg() . "\n";
        echo "   响应内容: " . substr($response, 0, 300) . "...\n\n";
        return false;
    }
    
    // 检查响应格式
    if (!isset($jsonData['status'])) {
        echo "   ❌ 响应格式错误: 缺少status字段\n";
        echo "   响应内容: " . json_encode($jsonData, JSON_UNESCAPED_UNICODE) . "\n\n";
        return false;
    }
    
    $status = $jsonData['status'];
    $message = $jsonData['message'] ?? '无消息';
    
    if ($status === 'success' || $status === 'healthy') {
        echo "   ✅ 测试通过\n";
        echo "   状态: $status\n";
        echo "   消息: $message\n";
        
        // 显示数据摘要
        if (isset($jsonData['data'])) {
            $data = $jsonData['data'];
            if (is_array($data)) {
                if (isset($data['total'])) {
                    echo "   数据总数: " . $data['total'] . "\n";
                }
                if (isset($data['articles']) && is_array($data['articles'])) {
                    echo "   文章数量: " . count($data['articles']) . "\n";
                    if (isset($data['pagination'])) {
                        echo "   分页信息: 第{$data['pagination']['current_page']}页，共{$data['pagination']['last_page']}页\n";
                    }
                }
                if (isset($data['categories']) && is_array($data['categories'])) {
                    echo "   分类数量: " . count($data['categories']) . "\n";
                }
                if (isset($data['tree']) && is_array($data['tree'])) {
                    echo "   分类树节点: " . count($data['tree']) . "\n";
                }
                if (isset($data['modules']) && is_array($data['modules'])) {
                    echo "   模块数量: " . count($data['modules']) . "\n";
                }
            }
        }
        echo "\n";
        return true;
    } else {
        echo "   ❌ 业务错误\n";
        echo "   状态: $status\n";
        echo "   消息: $message\n";
        if (isset($jsonData['error'])) {
            echo "   错误详情: " . $jsonData['error'] . "\n";
        }
        echo "\n";
        return false;
    }
}

// 执行最终测试
echo "🎯 开始最终API测试...\n\n";

$results = [];
$totalTests = count($finalTestCases);
$passedTests = 0;
$totalResponseTime = 0;

foreach ($finalTestCases as $testCase) {
    $startTime = microtime(true);
    $success = runFinalTest($testCase);
    $endTime = microtime(true);
    
    $responseTime = round(($endTime - $startTime) * 1000, 2);
    $totalResponseTime += $responseTime;
    
    $results[] = [
        'name' => $testCase['name'],
        'url' => $testCase['url'],
        'success' => $success,
        'response_time' => $responseTime
    ];
    
    if ($success) {
        $passedTests++;
    }
    
    // 短暂延迟避免请求过快
    usleep(200000); // 0.2秒
}

// 生成最终报告
echo "🎊 最终测试结果统计\n";
echo "====================\n";
echo "总测试数: $totalTests\n";
echo "通过数: $passedTests\n";
echo "失败数: " . ($totalTests - $passedTests) . "\n";
echo "通过率: " . round(($passedTests / $totalTests) * 100, 2) . "%\n";
echo "平均响应时间: " . round($totalResponseTime / $totalTests, 2) . "ms\n\n";

echo "📋 详细测试结果\n";
echo "================\n";
foreach ($results as $result) {
    $status = $result['success'] ? '✅ 通过' : '❌ 失败';
    $time = $result['response_time'] . 'ms';
    echo "- {$result['name']}: $status ($time)\n";
}

// 保存最终报告
$report = [
    'test_time' => date('Y-m-d H:i:s'),
    'summary' => [
        'total_tests' => $totalTests,
        'passed_tests' => $passedTests,
        'failed_tests' => $totalTests - $passedTests,
        'pass_rate' => round(($passedTests / $totalTests) * 100, 2),
        'average_response_time' => round($totalResponseTime / $totalTests, 2)
    ],
    'results' => $results
];

file_put_contents('final_api_test_report.json', json_encode($report, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

echo "\n📄 最终测试报告已保存到: final_api_test_report.json\n";

if ($passedTests === $totalTests) {
    echo "\n🎉 所有测试通过！API系统完美运行！\n";
    echo "🏆 恭喜！您的API系统已经达到100%可用性！\n";
} elseif ($passedTests >= $totalTests * 0.9) {
    echo "\n🎊 90%以上测试通过！API系统运行优秀！\n";
} elseif ($passedTests >= $totalTests * 0.8) {
    echo "\n✅ 80%以上测试通过！API系统运行良好！\n";
} else {
    echo "\n⚠️ 部分测试失败，需要进一步优化。\n";
}

echo "\n🎯 最终API测试完成！\n";
