# Nginx配置修复指南

## 问题描述
当前 `http://news.test.jijiaox.com/category/1` 返回404错误，这是因为nginx没有正确配置URL重写规则来支持ThinkPHP的路由。

## 解决方案

### 方案一：修改nginx配置（推荐）

请将以下配置添加到您的nginx站点配置文件中：

```nginx
server {
    listen 80;
    server_name news.test.jijiaox.com;
    root /www/wwwroot/news.test.jijiaox.com/public;
    index index.php index.html index.htm;

    # 处理静态文件
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri =404;
    }

    # 处理ThinkPHP路由
    location / {
        try_files $uri $uri/ /index.php$is_args$args;
    }

    # 处理PHP文件
    location ~ \.php$ {
        fastcgi_pass 127.0.0.1:9000;  # 根据您的PHP-FPM配置调整
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
        
        # 支持PATH_INFO
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_param PATH_INFO $fastcgi_path_info;
    }

    # 禁止访问隐藏文件
    location ~ /\. {
        deny all;
    }

    # 禁止访问敏感目录
    location ~ ^/(app|config|database|runtime|vendor)/ {
        deny all;
    }
}
```

### 方案二：使用查询字符串方式（临时方案）

如果无法修改nginx配置，可以使用以下URL格式访问：

- 分类页面：`http://news.test.jijiaox.com/index.php?s=/category/1`
- 文章页面：`http://news.test.jijiaox.com/index.php?s=/article/1`

## 验证修复

修改nginx配置后，重启nginx服务：

```bash
sudo nginx -t  # 检查配置语法
sudo systemctl reload nginx  # 重新加载配置
```

然后测试URL：

```bash
curl -s -o /dev/null -w "%{http_code}" http://news.test.jijiaox.com/category/1
```

应该返回200状态码。

## 当前状态

- ✅ PHP正常工作
- ✅ ThinkPHP入口文件正常
- ✅ 首页正常访问
- ❌ URL重写规则需要配置
- ✅ 路由配置正确

## 注意事项

1. nginx不支持.htaccess文件，需要在nginx配置中设置重写规则
2. 确保PHP-FPM正确配置并支持PATH_INFO
3. 确保nginx有权限访问项目目录
