#!/bin/bash

echo "🔧 自动修复nginx配置脚本"
echo "=========================="

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请使用root权限运行此脚本"
    echo "   sudo bash docs/auto_fix_nginx.sh"
    exit 1
fi

# 备份当前nginx配置
NGINX_CONF="/etc/nginx/sites-available/news.test.jijiaox.com"
NGINX_ENABLED="/etc/nginx/sites-enabled/news.test.jijiaox.com"

echo "📋 检查nginx配置文件..."

# 查找可能的配置文件位置
POSSIBLE_CONFIGS=(
    "/etc/nginx/sites-available/news.test.jijiaox.com"
    "/etc/nginx/conf.d/news.test.jijiaox.com.conf"
    "/etc/nginx/vhost/news.test.jijiaox.com.conf"
    "/www/server/panel/vhost/nginx/news.test.jijiaox.com.conf"
)

CONFIG_FILE=""
for conf in "${POSSIBLE_CONFIGS[@]}"; do
    if [ -f "$conf" ]; then
        CONFIG_FILE="$conf"
        echo "✅ 找到配置文件: $conf"
        break
    fi
done

if [ -z "$CONFIG_FILE" ]; then
    echo "❌ 未找到nginx配置文件"
    echo "   请手动创建配置文件或联系系统管理员"
    exit 1
fi

# 备份原配置
echo "💾 备份原配置文件..."
cp "$CONFIG_FILE" "${CONFIG_FILE}.backup.$(date +%Y%m%d_%H%M%S)"

# 创建新的nginx配置
echo "🔨 创建新的nginx配置..."
cat > "$CONFIG_FILE" << 'EOF'
server {
    listen 80;
    server_name news.test.jijiaox.com;
    root /www/wwwroot/news.test.jijiaox.com/public;
    index index.php index.html index.htm;

    # 处理静态文件
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri =404;
    }

    # 处理ThinkPHP路由
    location / {
        try_files $uri $uri/ /index.php$is_args$args;
    }

    # 处理PHP文件
    location ~ \.php$ {
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
        
        # 支持PATH_INFO
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_param PATH_INFO $fastcgi_path_info;
    }

    # 禁止访问隐藏文件
    location ~ /\. {
        deny all;
    }

    # 禁止访问敏感目录
    location ~ ^/(app|config|database|runtime|vendor)/ {
        deny all;
    }
}
EOF

# 测试nginx配置
echo "🧪 测试nginx配置..."
nginx -t

if [ $? -eq 0 ]; then
    echo "✅ nginx配置语法正确"
    
    # 重新加载nginx
    echo "🔄 重新加载nginx..."
    systemctl reload nginx
    
    if [ $? -eq 0 ]; then
        echo "✅ nginx重新加载成功"
        
        # 测试URL
        echo "🧪 测试URL访问..."
        sleep 2
        HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" http://news.test.jijiaox.com/category/1)
        
        if [ "$HTTP_CODE" = "200" ]; then
            echo "🎉 修复成功！URL现在可以正常访问了"
            echo "   测试URL: http://news.test.jijiaox.com/category/1"
        else
            echo "⚠️  nginx配置已更新，但URL仍返回 $HTTP_CODE"
            echo "   可能需要额外的配置调整"
        fi
    else
        echo "❌ nginx重新加载失败"
    fi
else
    echo "❌ nginx配置语法错误，请检查配置"
fi

echo ""
echo "📋 修复完成报告："
echo "   - 原配置已备份"
echo "   - 新配置已应用"
echo "   - 请测试网站功能"
