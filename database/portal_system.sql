-- 门户配置系统数据库表结构
-- 创建时间：2025-01-10

-- 门户基础配置表（KV存储）
CREATE TABLE `portal_configs` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `config_key` varchar(100) NOT NULL COMMENT '配置键名',
  `config_value` text COMMENT '配置值',
  `config_type` enum('string','number','boolean','json') NOT NULL DEFAULT 'string' COMMENT '配置类型',
  `group_name` varchar(50) DEFAULT 'default' COMMENT '配置分组',
  `description` varchar(255) DEFAULT NULL COMMENT '配置描述',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序权重',
  `is_system` tinyint(1) DEFAULT 0 COMMENT '是否系统配置：1是，0否',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`),
  KEY `idx_group_name` (`group_name`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='门户基础配置表';

-- 门户模块配置表
CREATE TABLE `portal_modules` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `module_name` varchar(50) NOT NULL COMMENT '模块名称（英文标识）',
  `module_title` varchar(100) NOT NULL COMMENT '模块标题（中文显示）',
  `module_description` varchar(255) DEFAULT NULL COMMENT '模块描述',
  `column_count` tinyint(1) NOT NULL DEFAULT 1 COMMENT '列数：1或2',
  `config_data` json DEFAULT NULL COMMENT '模块配置数据（JSON格式）',
  `is_enabled` tinyint(1) DEFAULT 1 COMMENT '是否启用：1启用，0禁用',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序权重',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_module_name` (`module_name`),
  KEY `idx_is_enabled` (`is_enabled`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='门户模块配置表';

-- 插入基础配置示例数据
INSERT INTO `portal_configs` (`config_key`, `config_value`, `config_type`, `group_name`, `description`, `sort_order`, `is_system`, `create_time`, `update_time`) VALUES
('site_name', '新闻门户系统', 'string', 'basic', '网站名称', 1, 1, NOW(), NOW()),
('site_logo', '/static/images/logo.png', 'string', 'basic', '网站Logo', 2, 1, NOW(), NOW()),
('site_keywords', '新闻,资讯,门户', 'string', 'seo', 'SEO关键词', 1, 0, NOW(), NOW()),
('site_description', '专业的新闻资讯门户网站', 'string', 'seo', 'SEO描述', 2, 0, NOW(), NOW()),
('max_upload_size', '10', 'number', 'upload', '最大上传文件大小(MB)', 1, 0, NOW(), NOW()),
('enable_comment', 'true', 'boolean', 'feature', '是否开启评论功能', 1, 0, NOW(), NOW()),
('cache_expire', '3600', 'number', 'performance', '缓存过期时间(秒)', 1, 0, NOW(), NOW()),
('theme_config', '{"primary_color":"#1890ff","layout":"default"}', 'json', 'theme', '主题配置', 1, 0, NOW(), NOW());

-- 插入模块配置示例数据
INSERT INTO `portal_modules` (`module_name`, `module_title`, `module_description`, `column_count`, `config_data`, `is_enabled`, `sort_order`, `create_time`, `update_time`) VALUES
('news_banner', '新闻轮播', '首页新闻轮播模块', 1, '{"auto_play":true,"interval":3000,"show_dots":true,"height":"400px"}', 1, 1, NOW(), NOW()),
('hot_news', '热门新闻', '热门新闻列表展示', 2, '{"limit":10,"show_image":true,"show_date":true}', 1, 2, NOW(), NOW()),
('category_nav', '分类导航', '新闻分类导航菜单', 1, '{"style":"horizontal","show_icon":true,"max_items":8}', 1, 3, NOW(), NOW()),
('latest_articles', '最新文章', '最新发布的文章列表', 2, '{"limit":6,"show_summary":true,"summary_length":100}', 1, 4, NOW(), NOW()),
('sidebar_ads', '侧边广告', '侧边栏广告位', 1, '{"position":"right","width":"300px","height":"250px"}', 1, 5, NOW(), NOW()),
('footer_links', '底部链接', '网站底部友情链接', 1, '{"columns":4,"show_title":true,"target":"_blank"}', 1, 6, NOW(), NOW());
