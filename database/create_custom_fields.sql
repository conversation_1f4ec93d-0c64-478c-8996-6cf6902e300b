-- 创建自定义字段相关表（简化版，不使用触发器）

-- 文章自定义字段定义表
CREATE TABLE IF NOT EXISTS `article_custom_fields` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '字段ID',
  `name` varchar(100) NOT NULL COMMENT '字段显示名称',
  `field_key` varchar(100) NOT NULL COMMENT '字段键名，用于程序调用，必须唯一',
  `field_type` enum('tag','text','textarea') NOT NULL DEFAULT 'text' COMMENT '字段类型：tag标签,text文本,textarea长文本',
  `is_required` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否必填：0否，1是',
  `default_value` text COMMENT '默认值',
  `options` json DEFAULT NULL COMMENT '字段选项配置，JSON格式，用于扩展配置',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序权重，数字越大越靠前',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用：0禁用，1启用',
  `description` text COMMENT '字段描述说明',
  `validation_rules` json DEFAULT NULL COMMENT '验证规则，JSON格式存储',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_field_key` (`field_key`),
  KEY `idx_field_type` (`field_type`),
  KEY `idx_is_required` (`is_required`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文章自定义字段定义表';

-- 文章自定义字段值表
CREATE TABLE IF NOT EXISTS `article_custom_field_values` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `article_id` int(11) unsigned NOT NULL COMMENT '文章ID，关联articles表',
  `field_id` int(11) unsigned NOT NULL COMMENT '字段ID，关联article_custom_fields表',
  `field_value` longtext COMMENT '字段值，支持大文本存储',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_article_field` (`article_id`, `field_id`),
  KEY `idx_article_id` (`article_id`),
  KEY `idx_field_id` (`field_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文章自定义字段值表';

-- 插入示例自定义字段
INSERT INTO `article_custom_fields` (`name`, `field_key`, `field_type`, `is_required`, `default_value`, `options`, `sort_order`, `is_active`, `description`, `validation_rules`, `create_time`, `update_time`) VALUES
('文章标签', 'article_tags', 'tag', 0, NULL, '{"placeholder": "请输入标签，多个标签用逗号分隔", "max_tags": 10}', 100, 1, '用于标记文章的关键词标签', '{"max_length": 500}', NOW(), NOW()),
('来源', 'source', 'text', 0, NULL, '{"placeholder": "请输入文章来源"}', 90, 1, '文章的来源信息', '{"max_length": 100}', NOW(), NOW()),
('编辑备注', 'editor_note', 'textarea', 0, NULL, '{"placeholder": "编辑备注信息", "rows": 3}', 80, 1, '编辑人员的内部备注信息', '{"max_length": 1000}', NOW(), NOW()),
('外部链接', 'external_link', 'text', 0, NULL, '{"placeholder": "https://example.com"}', 70, 1, '相关的外部链接地址', '{"max_length": 500, "pattern": "url"}', NOW(), NOW()),
('重点摘要', 'highlight_summary', 'textarea', 0, NULL, '{"placeholder": "文章重点内容摘要", "rows": 4}', 60, 1, '文章的重点内容摘要，用于特殊展示', '{"max_length": 2000}', NOW(), NOW());

-- 为现有文章添加一些示例自定义字段值
INSERT INTO `article_custom_field_values` (`article_id`, `field_id`, `field_value`, `create_time`, `update_time`) VALUES
-- 第一篇文章（全国两会）
(1, 1, '["时政", "两会", "代表委员", "建言献策"]', NOW(), NOW()),
(1, 2, '新华社', NOW(), NOW()),
(1, 5, '2025年全国两会即将召开，代表委员围绕经济发展、民生改善等重要议题积极准备提案建议，体现了人民当家作主的制度优势。', NOW(), NOW()),

-- 第二篇文章（国务院会议）
(2, 1, '["国务院", "常务会议", "重点工作", "经济发展"]', NOW(), NOW()),
(2, 2, '人民日报', NOW(), NOW()),

-- 第三篇文章（ChatGPT-5）
(3, 1, '["ChatGPT-5", "OpenAI", "人工智能", "AI技术", "科技突破"]', NOW(), NOW()),
(3, 2, '科技日报', NOW(), NOW()),
(3, 4, 'https://openai.com', NOW(), NOW()),
(3, 5, 'ChatGPT-5在推理能力、多模态交互等方面实现重大突破，标志着AI技术进入新的发展阶段，将为用户带来更智能、安全的AI体验。', NOW(), NOW()),

-- 第四篇文章（互联网趋势）
(4, 1, '["Web3.0", "区块链", "元宇宙", "互联网趋势"]', NOW(), NOW()),
(4, 2, '互联网观察', NOW(), NOW()),

-- 第五篇文章（中国足球）
(5, 1, '["中国足球", "青训改革", "足球政策", "体育发展"]', NOW(), NOW()),
(5, 2, '体坛周报', NOW(), NOW());
