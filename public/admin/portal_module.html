<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>门户模块配置管理</title>
    <link rel="stylesheet" href="/static/layui/css/layui.css">
    <style>
        .layui-container {
            padding: 20px;
        }
        .module-toolbar {
            margin-bottom: 15px;
        }
        .module-card {
            margin-bottom: 15px;
            border: 1px solid #e6e6e6;
            border-radius: 6px;
            transition: all 0.3s;
            cursor: move;
        }
        .module-card:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .module-card.disabled {
            opacity: 0.6;
            background-color: #f5f5f5;
        }
        .module-header {
            padding: 15px 20px;
            border-bottom: 1px solid #e6e6e6;
            background-color: #fafafa;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .module-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }
        .module-name {
            font-size: 12px;
            color: #999;
            margin-left: 10px;
        }
        .module-status {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .status-enabled {
            color: #52c41a;
            font-weight: bold;
        }
        .status-disabled {
            color: #f5222d;
            font-weight: bold;
        }
        .column-badge {
            background-color: #1890ff;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
        }
        .module-body {
            padding: 20px;
        }
        .module-description {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.6;
        }
        .config-preview {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 120px;
            overflow-y: auto;
        }
        .module-actions {
            margin-top: 15px;
            text-align: right;
        }
        .drag-handle {
            cursor: move;
            color: #999;
            margin-right: 10px;
        }
        .drag-handle:hover {
            color: #333;
        }
        .json-editor {
            min-height: 200px;
            font-family: 'Courier New', monospace;
        }
        .sortable-ghost {
            opacity: 0.4;
        }
        .sortable-chosen {
            transform: scale(1.02);
        }
    </style>
</head>
<body>
    <div class="layui-container">
        <div class="layui-card">
            <div class="layui-card-header">
                <h2>门户模块配置管理</h2>
            </div>
            <div class="layui-card-body">
                <!-- 工具栏 -->
                <div class="module-toolbar">
                    <div class="layui-row">
                        <div class="layui-col-md8">
                            <div class="layui-inline">
                                <label class="layui-form-label" style="width: 80px;">关键词:</label>
                                <div class="layui-input-inline" style="width: 200px;">
                                    <input type="text" id="keywordSearch" placeholder="搜索模块名称或标题" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn layui-btn-sm" id="searchBtn">
                                    <i class="layui-icon layui-icon-search"></i> 搜索
                                </button>
                                <button class="layui-btn layui-btn-sm layui-btn-primary" id="resetBtn">
                                    <i class="layui-icon layui-icon-refresh"></i> 重置
                                </button>
                            </div>
                        </div>
                        <div class="layui-col-md4" style="text-align: right;">
                            <button class="layui-btn layui-btn-sm" id="addModuleBtn">
                                <i class="layui-icon layui-icon-add-1"></i> 新增模块
                            </button>
                            <button class="layui-btn layui-btn-sm layui-btn-normal" id="saveSortBtn" style="display: none;">
                                <i class="layui-icon layui-icon-ok"></i> 保存排序
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 模块列表 -->
                <div id="moduleList" class="module-list">
                    <!-- 模块卡片将通过JavaScript动态生成 -->
                </div>

                <!-- 分页 -->
                <div id="modulePagination"></div>
            </div>
        </div>
    </div>

    <!-- 模块编辑表单 -->
    <div id="moduleForm" style="display: none; padding: 20px;">
        <form class="layui-form" lay-filter="moduleFormFilter">
            <input type="hidden" name="id">
            
            <div class="layui-form-item">
                <label class="layui-form-label">模块名称 <span style="color: red;">*</span></label>
                <div class="layui-input-block">
                    <input type="text" name="module_name" required lay-verify="required|moduleName" 
                           placeholder="请输入模块名称（英文标识，如：news_banner）" class="layui-input">
                    <div class="layui-word-aux">模块的英文标识，用于程序调用，只能包含字母、数字和下划线</div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">模块标题 <span style="color: red;">*</span></label>
                <div class="layui-input-block">
                    <input type="text" name="module_title" required lay-verify="required" 
                           placeholder="请输入模块标题（中文显示名称）" class="layui-input">
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">模块描述</label>
                <div class="layui-input-block">
                    <textarea name="module_description" placeholder="请输入模块描述" class="layui-textarea"></textarea>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">列数设置 <span style="color: red;">*</span></label>
                <div class="layui-input-block">
                    <select name="column_count" required>
                        <option value="">请选择列数</option>
                        <option value="1">单列布局</option>
                        <option value="2">双列布局</option>
                    </select>
                    <div class="layui-word-aux">选择模块在页面中的列数布局</div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">配置数据</label>
                <div class="layui-input-block">
                    <textarea name="config_data" placeholder="请输入JSON格式的配置数据" 
                              class="layui-textarea json-editor"></textarea>
                    <div class="layui-word-aux">JSON格式的模块配置数据，如：{"limit":10,"show_image":true}</div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">排序权重</label>
                <div class="layui-input-block">
                    <input type="number" name="sort_order" placeholder="数字越小排序越靠前（默认：0）" class="layui-input">
                </div>
            </div>
            
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <input type="checkbox" name="is_enabled" title="启用模块" lay-skin="primary" checked>
                </div>
            </div>
            
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="moduleSubmit">保存</button>
                    <button type="button" class="layui-btn layui-btn-primary" id="cancelModuleBtn">取消</button>
                </div>
            </div>
        </form>
    </div>

    <!-- 模块配置编辑表单 -->
    <div id="configForm" style="display: none; padding: 20px;">
        <form class="layui-form" lay-filter="configFormFilter">
            <input type="hidden" name="module_name">
            
            <div class="layui-form-item">
                <label class="layui-form-label">配置数据</label>
                <div class="layui-input-block">
                    <textarea name="config_data" placeholder="请输入JSON格式的配置数据" 
                              class="layui-textarea json-editor"></textarea>
                    <div class="layui-word-aux">
                        <p>JSON格式的模块配置数据，常用配置项：</p>
                        <ul style="margin: 5px 0; padding-left: 20px; font-size: 12px; color: #666;">
                            <li>limit: 显示数量限制</li>
                            <li>show_image: 是否显示图片</li>
                            <li>show_date: 是否显示日期</li>
                            <li>auto_play: 是否自动播放</li>
                            <li>interval: 播放间隔（毫秒）</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="configSubmit">保存配置</button>
                    <button type="button" class="layui-btn layui-btn-primary" id="cancelConfigBtn">取消</button>
                </div>
            </div>
        </form>
    </div>

    <script src="/static/layui/layui.js"></script>
    <!-- 引入Sortable.js用于拖拽排序 -->
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    <script>
        layui.use(['form', 'layer', 'laypage'], function(){
            var form = layui.form;
            var layer = layui.layer;
            var laypage = layui.laypage;
            var $ = layui.$;

            var currentPage = 1;
            var pageSize = 10;
            var keyword = '';
            var sortable = null;

            // 初始化
            loadModules();

            // 搜索按钮
            $('#searchBtn').on('click', function() {
                keyword = $('#keywordSearch').val();
                currentPage = 1;
                loadModules();
            });

            // 重置按钮
            $('#resetBtn').on('click', function() {
                $('#keywordSearch').val('');
                keyword = '';
                currentPage = 1;
                loadModules();
            });

            // 回车搜索
            $('#keywordSearch').on('keydown', function(e) {
                if (e.keyCode === 13) {
                    keyword = $(this).val();
                    currentPage = 1;
                    loadModules();
                }
            });

            // 新增模块按钮
            $('#addModuleBtn').on('click', function() {
                showModuleForm();
            });

            // 保存排序按钮
            $('#saveSortBtn').on('click', function() {
                saveSortOrder();
            });

            // 加载模块列表
            function loadModules() {
                $.ajax({
                    url: '/api/admin/portal/modules/',
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + (localStorage.getItem('admin_token') || '')
                    },
                    data: {
                        page: currentPage,
                        limit: pageSize,
                        keyword: keyword
                    },
                    success: function(res) {
                        if (res.code === 200) {
                            renderModules(res.data.list);
                            renderPagination(res.data.total);
                            initSortable();
                        } else {
                            layer.msg(res.message || '加载模块列表失败');
                        }
                    },
                    error: function() {
                        layer.msg('请求失败');
                    }
                });
            }

            // 渲染模块列表
            function renderModules(modules) {
                var html = '';
                
                modules.forEach(function(module) {
                    var statusClass = module.is_enabled ? 'status-enabled' : 'status-disabled';
                    var statusText = module.is_enabled ? '已启用' : '已禁用';
                    var cardClass = module.is_enabled ? '' : 'disabled';
                    var configPreview = '';
                    
                    if (module.config_data && typeof module.config_data === 'object') {
                        configPreview = JSON.stringify(module.config_data, null, 2);
                    } else if (module.config_data) {
                        configPreview = module.config_data;
                    } else {
                        configPreview = '{}';
                    }
                    
                    html += `
                        <div class="module-card ${cardClass}" data-id="${module.id}" data-sort="${module.sort_order}">
                            <div class="module-header">
                                <div style="display: flex; align-items: center;">
                                    <i class="layui-icon layui-icon-cols drag-handle"></i>
                                    <span class="module-title">${module.module_title}</span>
                                    <span class="module-name">(${module.module_name})</span>
                                    <span class="column-badge">${module.column_count}列</span>
                                </div>
                                <div class="module-status">
                                    <span class="${statusClass}">${statusText}</span>
                                    <div class="layui-btn-group">
                                        <button class="layui-btn layui-btn-xs" onclick="editModule(${module.id})">
                                            <i class="layui-icon layui-icon-edit"></i> 编辑
                                        </button>
                                        <button class="layui-btn layui-btn-xs layui-btn-normal" onclick="editConfig('${module.module_name}')">
                                            <i class="layui-icon layui-icon-set"></i> 配置
                                        </button>
                                        <button class="layui-btn layui-btn-xs ${module.is_enabled ? 'layui-btn-warm' : 'layui-btn-primary'}" 
                                                onclick="toggleStatus(${module.id})">
                                            <i class="layui-icon ${module.is_enabled ? 'layui-icon-pause' : 'layui-icon-play'}"></i> 
                                            ${module.is_enabled ? '禁用' : '启用'}
                                        </button>
                                        <button class="layui-btn layui-btn-xs layui-btn-danger" onclick="deleteModule(${module.id})">
                                            <i class="layui-icon layui-icon-delete"></i> 删除
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="module-body">
                                ${module.module_description ? `<div class="module-description">${module.module_description}</div>` : ''}
                                <div class="config-preview">${configPreview}</div>
                            </div>
                        </div>
                    `;
                });
                
                $('#moduleList').html(html);
            }

            // 渲染分页
            function renderPagination(total) {
                laypage.render({
                    elem: 'modulePagination',
                    count: total,
                    limit: pageSize,
                    curr: currentPage,
                    layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
                    jump: function(obj, first) {
                        if (!first) {
                            currentPage = obj.curr;
                            pageSize = obj.limit;
                            loadModules();
                        }
                    }
                });
            }

            // 初始化拖拽排序
            function initSortable() {
                if (sortable) {
                    sortable.destroy();
                }
                
                var moduleList = document.getElementById('moduleList');
                if (moduleList) {
                    sortable = Sortable.create(moduleList, {
                        handle: '.drag-handle',
                        animation: 150,
                        ghostClass: 'sortable-ghost',
                        chosenClass: 'sortable-chosen',
                        onEnd: function(evt) {
                            $('#saveSortBtn').show();
                        }
                    });
                }
            }

            // 保存排序
            function saveSortOrder() {
                var sortData = [];
                $('#moduleList .module-card').each(function(index) {
                    sortData.push({
                        id: parseInt($(this).data('id')),
                        sort_order: index + 1
                    });
                });
                
                $.ajax({
                    url: '/api/admin/portal/modules/sort',
                    method: 'POST',
                    headers: {
                        'Authorization': 'Bearer ' + (localStorage.getItem('admin_token') || ''),
                        'Content-Type': 'application/json'
                    },
                    data: JSON.stringify({sort_data: sortData}),
                    success: function(res) {
                        if (res.code === 200) {
                            layer.msg('排序保存成功');
                            $('#saveSortBtn').hide();
                            loadModules();
                        } else {
                            layer.msg(res.message || '排序保存失败');
                        }
                    },
                    error: function() {
                        layer.msg('请求失败');
                    }
                });
            }

            // 显示模块表单
            function showModuleForm(data) {
                var title = data ? '编辑模块' : '新增模块';
                var isEdit = !!data;
                
                layer.open({
                    type: 1,
                    title: title,
                    content: $('#moduleForm'),
                    area: ['700px', '600px'],
                    success: function(layero, index) {
                        // 重置表单
                        form.val('moduleFormFilter', {});
                        
                        if (isEdit) {
                            // 编辑模式，填充数据
                            var formData = Object.assign({}, data);
                            if (formData.config_data && typeof formData.config_data === 'object') {
                                formData.config_data = JSON.stringify(formData.config_data, null, 2);
                            }
                            form.val('moduleFormFilter', formData);
                        }
                        
                        form.render();
                    }
                });
            }

            // 全局函数：编辑模块
            window.editModule = function(id) {
                $.ajax({
                    url: '/api/admin/portal/modules/' + id,
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + (localStorage.getItem('admin_token') || '')
                    },
                    success: function(res) {
                        if (res.code === 200) {
                            showModuleForm(res.data);
                        } else {
                            layer.msg(res.message || '获取模块信息失败');
                        }
                    },
                    error: function() {
                        layer.msg('请求失败');
                    }
                });
            };

            // 全局函数：编辑配置
            window.editConfig = function(moduleName) {
                $.ajax({
                    url: '/api/admin/portal/modules/' + moduleName + '/config',
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + (localStorage.getItem('admin_token') || '')
                    },
                    success: function(res) {
                        if (res.code === 200) {
                            showConfigForm(moduleName, res.data);
                        } else {
                            layer.msg(res.message || '获取模块配置失败');
                        }
                    },
                    error: function() {
                        layer.msg('请求失败');
                    }
                });
            };

            // 显示配置表单
            function showConfigForm(moduleName, configData) {
                layer.open({
                    type: 1,
                    title: '编辑模块配置 - ' + moduleName,
                    content: $('#configForm'),
                    area: ['600px', '500px'],
                    success: function(layero, index) {
                        var configJson = '';
                        if (configData && typeof configData === 'object') {
                            configJson = JSON.stringify(configData, null, 2);
                        } else if (configData) {
                            configJson = configData;
                        } else {
                            configJson = '{}';
                        }
                        
                        form.val('configFormFilter', {
                            module_name: moduleName,
                            config_data: configJson
                        });
                        
                        form.render();
                    }
                });
            }

            // 全局函数：切换状态
            window.toggleStatus = function(id) {
                $.ajax({
                    url: '/api/admin/portal/modules/' + id + '/toggle',
                    method: 'POST',
                    headers: {
                        'Authorization': 'Bearer ' + (localStorage.getItem('admin_token') || '')
                    },
                    success: function(res) {
                        if (res.code === 200) {
                            layer.msg('状态切换成功');
                            loadModules();
                        } else {
                            layer.msg(res.message || '状态切换失败');
                        }
                    },
                    error: function() {
                        layer.msg('请求失败');
                    }
                });
            };

            // 全局函数：删除模块
            window.deleteModule = function(id) {
                layer.confirm('确定要删除这个模块吗？', function(index) {
                    $.ajax({
                        url: '/api/admin/portal/modules/' + id,
                        method: 'DELETE',
                        headers: {
                            'Authorization': 'Bearer ' + (localStorage.getItem('admin_token') || '')
                        },
                        success: function(res) {
                            if (res.code === 200) {
                                layer.msg('删除成功');
                                loadModules();
                            } else {
                                layer.msg(res.message || '删除失败');
                            }
                        },
                        error: function() {
                            layer.msg('请求失败');
                        }
                    });
                    layer.close(index);
                });
            };

            // 自定义验证规则
            form.verify({
                moduleName: function(value) {
                    if (!/^[a-zA-Z][a-zA-Z0-9_]*$/.test(value)) {
                        return '模块名称格式不正确，只能包含字母、数字和下划线，且必须以字母开头';
                    }
                }
            });

            // 模块表单提交
            form.on('submit(moduleSubmit)', function(data) {
                var formData = data.field;
                var isEdit = !!formData.id;
                
                // 处理配置数据
                if (formData.config_data) {
                    try {
                        formData.config_data = JSON.parse(formData.config_data);
                    } catch (e) {
                        layer.msg('配置数据JSON格式不正确');
                        return false;
                    }
                } else {
                    formData.config_data = {};
                }
                
                var url = isEdit ? '/api/admin/portal/modules/' + formData.id : '/api/admin/portal/modules/';
                var method = isEdit ? 'PUT' : 'POST';
                
                $.ajax({
                    url: url,
                    method: method,
                    headers: {
                        'Authorization': 'Bearer ' + (localStorage.getItem('admin_token') || ''),
                        'Content-Type': 'application/json'
                    },
                    data: JSON.stringify(formData),
                    success: function(res) {
                        if (res.code === 200) {
                            layer.msg(isEdit ? '更新成功' : '创建成功');
                            layer.closeAll();
                            loadModules();
                        } else {
                            layer.msg(res.message || (isEdit ? '更新失败' : '创建失败'));
                        }
                    },
                    error: function() {
                        layer.msg('请求失败');
                    }
                });
                
                return false;
            });

            // 配置表单提交
            form.on('submit(configSubmit)', function(data) {
                var formData = data.field;
                
                // 处理配置数据
                if (formData.config_data) {
                    try {
                        var configData = JSON.parse(formData.config_data);
                    } catch (e) {
                        layer.msg('配置数据JSON格式不正确');
                        return false;
                    }
                } else {
                    var configData = {};
                }
                
                $.ajax({
                    url: '/api/admin/portal/modules/' + formData.module_name + '/config',
                    method: 'POST',
                    headers: {
                        'Authorization': 'Bearer ' + (localStorage.getItem('admin_token') || ''),
                        'Content-Type': 'application/json'
                    },
                    data: JSON.stringify({config_data: configData}),
                    success: function(res) {
                        if (res.code === 200) {
                            layer.msg('配置保存成功');
                            layer.closeAll();
                            loadModules();
                        } else {
                            layer.msg(res.message || '配置保存失败');
                        }
                    },
                    error: function() {
                        layer.msg('请求失败');
                    }
                });
                
                return false;
            });

            // 取消按钮
            $('#cancelModuleBtn, #cancelConfigBtn').on('click', function() {
                layer.closeAll();
            });
        });
    </script>
</body>
</html>
