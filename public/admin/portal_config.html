<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>门户基础配置管理</title>
    <link rel="stylesheet" href="/static/layui/css/layui.css">
    <style>
        .layui-container {
            padding: 20px;
        }
        .config-toolbar {
            margin-bottom: 15px;
        }
        .config-type-tag {
            font-size: 12px;
            padding: 2px 6px;
            border-radius: 3px;
            color: #fff;
        }
        .type-string { background-color: #1890ff; }
        .type-number { background-color: #52c41a; }
        .type-boolean { background-color: #fa8c16; }
        .type-json { background-color: #722ed1; }
        .system-config { color: #f5222d; font-weight: bold; }
        .json-editor {
            min-height: 120px;
            font-family: 'Courier New', monospace;
        }
    </style>
</head>
<body>
    <div class="layui-container">
        <div class="layui-card">
            <div class="layui-card-header">
                <h2>门户基础配置管理</h2>
            </div>
            <div class="layui-card-body">
                <!-- 工具栏 -->
                <div class="config-toolbar">
                    <div class="layui-row">
                        <div class="layui-col-md8">
                            <div class="layui-inline">
                                <label class="layui-form-label" style="width: 80px;">分组筛选:</label>
                                <div class="layui-input-inline" style="width: 150px;">
                                    <select id="groupFilter" lay-filter="groupFilter">
                                        <option value="">全部分组</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label" style="width: 80px;">关键词:</label>
                                <div class="layui-input-inline" style="width: 200px;">
                                    <input type="text" id="keywordSearch" placeholder="搜索配置键名或描述" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn layui-btn-sm" id="searchBtn">
                                    <i class="layui-icon layui-icon-search"></i> 搜索
                                </button>
                                <button class="layui-btn layui-btn-sm layui-btn-primary" id="resetBtn">
                                    <i class="layui-icon layui-icon-refresh"></i> 重置
                                </button>
                            </div>
                        </div>
                        <div class="layui-col-md4" style="text-align: right;">
                            <button class="layui-btn layui-btn-sm" id="addConfigBtn">
                                <i class="layui-icon layui-icon-add-1"></i> 新增配置
                            </button>
                            <button class="layui-btn layui-btn-sm layui-btn-warm" id="clearCacheBtn">
                                <i class="layui-icon layui-icon-refresh-1"></i> 清除缓存
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 配置表格 -->
                <table class="layui-hide" id="configTable" lay-filter="configTable"></table>
            </div>
        </div>
    </div>

    <!-- 配置编辑表单 -->
    <div id="configForm" style="display: none; padding: 20px;">
        <form class="layui-form" lay-filter="configFormFilter">
            <input type="hidden" name="id">
            
            <div class="layui-form-item">
                <label class="layui-form-label">配置键名 <span style="color: red;">*</span></label>
                <div class="layui-input-block">
                    <input type="text" name="config_key" required lay-verify="required|configKey" 
                           placeholder="请输入配置键名（英文字母开头，只能包含字母、数字、下划线）" class="layui-input">
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">配置类型 <span style="color: red;">*</span></label>
                <div class="layui-input-block">
                    <select name="config_type" lay-filter="configType" required>
                        <option value="">请选择配置类型</option>
                        <option value="string">字符串 (string)</option>
                        <option value="number">数字 (number)</option>
                        <option value="boolean">布尔值 (boolean)</option>
                        <option value="json">JSON对象 (json)</option>
                    </select>
                </div>
            </div>
            
            <div class="layui-form-item" id="configValueItem">
                <label class="layui-form-label">配置值 <span style="color: red;">*</span></label>
                <div class="layui-input-block">
                    <input type="text" name="config_value" required lay-verify="required" 
                           placeholder="请输入配置值" class="layui-input" id="configValueInput">
                    <textarea name="config_value_json" placeholder="请输入JSON格式的配置值" 
                              class="layui-textarea json-editor" id="configValueJson" style="display: none;"></textarea>
                    <select name="config_value_bool" id="configValueBool" style="display: none;">
                        <option value="">请选择</option>
                        <option value="true">是 (true)</option>
                        <option value="false">否 (false)</option>
                    </select>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">配置分组</label>
                <div class="layui-input-block">
                    <input type="text" name="group_name" placeholder="请输入配置分组（默认：default）" class="layui-input">
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">配置描述</label>
                <div class="layui-input-block">
                    <textarea name="description" placeholder="请输入配置描述" class="layui-textarea"></textarea>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">排序权重</label>
                <div class="layui-input-block">
                    <input type="number" name="sort_order" placeholder="数字越小排序越靠前（默认：0）" class="layui-input">
                </div>
            </div>
            
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <input type="checkbox" name="is_system" title="系统配置（系统配置不允许删除）" lay-skin="primary">
                </div>
            </div>
            
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="configSubmit">保存</button>
                    <button type="button" class="layui-btn layui-btn-primary" id="cancelBtn">取消</button>
                </div>
            </div>
        </form>
    </div>

    <!-- 表格操作列模板 -->
    <script type="text/html" id="configTableBar">
        <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
        {{# if(d.is_system == 0) { }}
        <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
        {{# } }}
    </script>

    <!-- 配置类型模板 -->
    <script type="text/html" id="configTypeTpl">
        <span class="config-type-tag type-{{d.config_type}}">{{d.config_type}}</span>
    </script>

    <!-- 系统配置模板 -->
    <script type="text/html" id="systemConfigTpl">
        {{# if(d.is_system == 1) { }}
        <span class="system-config">系统</span>
        {{# } else { }}
        <span>普通</span>
        {{# } }}
    </script>

    <script src="/static/layui/layui.js"></script>
    <script>
        layui.use(['table', 'form', 'layer', 'element'], function(){
            var table = layui.table;
            var form = layui.form;
            var layer = layui.layer;
            var $ = layui.$;

            // 配置表格
            var configTable = table.render({
                elem: '#configTable',
                url: '/api/admin/portal/configs/',
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + (localStorage.getItem('admin_token') || '')
                },
                page: true,
                limit: 20,
                limits: [10, 20, 50, 100],
                cols: [[
                    {field: 'id', title: 'ID', width: 80, sort: true},
                    {field: 'config_key', title: '配置键名', width: 200},
                    {field: 'config_value', title: '配置值', width: 250, templet: function(d) {
                        var value = d.config_value;
                        if (value && value.length > 50) {
                            return '<span title="' + value + '">' + value.substring(0, 50) + '...</span>';
                        }
                        return value;
                    }},
                    {field: 'config_type', title: '类型', width: 100, templet: '#configTypeTpl'},
                    {field: 'group_name', title: '分组', width: 120},
                    {field: 'description', title: '描述', width: 200},
                    {field: 'is_system', title: '类型', width: 80, templet: '#systemConfigTpl'},
                    {field: 'sort_order', title: '排序', width: 80, sort: true},
                    {field: 'update_time', title: '更新时间', width: 160},
                    {title: '操作', width: 150, toolbar: '#configTableBar', fixed: 'right'}
                ]],
                done: function(res, curr, count) {
                    // 表格渲染完成后的回调
                }
            });

            // 加载配置分组
            loadConfigGroups();

            function loadConfigGroups() {
                $.ajax({
                    url: '/api/admin/portal/configs/groups',
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + (localStorage.getItem('admin_token') || '')
                    },
                    success: function(res) {
                        if (res.code === 200) {
                            var html = '<option value="">全部分组</option>';
                            res.data.forEach(function(group) {
                                html += '<option value="' + group + '">' + group + '</option>';
                            });
                            $('#groupFilter').html(html);
                            form.render('select');
                        }
                    }
                });
            }

            // 分组筛选
            form.on('select(groupFilter)', function(data) {
                reloadTable();
            });

            // 搜索按钮
            $('#searchBtn').on('click', function() {
                reloadTable();
            });

            // 重置按钮
            $('#resetBtn').on('click', function() {
                $('#groupFilter').val('');
                $('#keywordSearch').val('');
                form.render('select');
                reloadTable();
            });

            // 回车搜索
            $('#keywordSearch').on('keydown', function(e) {
                if (e.keyCode === 13) {
                    reloadTable();
                }
            });

            function reloadTable() {
                var group = $('#groupFilter').val();
                var keyword = $('#keywordSearch').val();
                
                configTable.reload({
                    where: {
                        group: group,
                        keyword: keyword
                    },
                    page: {
                        curr: 1
                    }
                });
            }

            // 新增配置按钮
            $('#addConfigBtn').on('click', function() {
                showConfigForm();
            });

            // 清除缓存按钮
            $('#clearCacheBtn').on('click', function() {
                layer.confirm('确定要清除所有配置缓存吗？', function(index) {
                    $.ajax({
                        url: '/api/admin/portal/configs/clear-cache',
                        method: 'POST',
                        headers: {
                            'Authorization': 'Bearer ' + (localStorage.getItem('admin_token') || '')
                        },
                        success: function(res) {
                            if (res.code === 200) {
                                layer.msg('缓存清除成功');
                            } else {
                                layer.msg(res.message || '缓存清除失败');
                            }
                        },
                        error: function() {
                            layer.msg('请求失败');
                        }
                    });
                    layer.close(index);
                });
            });

            // 表格工具栏事件
            table.on('tool(configTable)', function(obj) {
                var data = obj.data;
                
                if (obj.event === 'edit') {
                    showConfigForm(data);
                } else if (obj.event === 'del') {
                    layer.confirm('确定要删除这个配置吗？', function(index) {
                        $.ajax({
                            url: '/api/admin/portal/configs/' + data.id,
                            method: 'DELETE',
                            headers: {
                                'Authorization': 'Bearer ' + (localStorage.getItem('admin_token') || '')
                            },
                            success: function(res) {
                                if (res.code === 200) {
                                    layer.msg('删除成功');
                                    obj.del();
                                    loadConfigGroups(); // 重新加载分组
                                } else {
                                    layer.msg(res.message || '删除失败');
                                }
                            },
                            error: function() {
                                layer.msg('请求失败');
                            }
                        });
                        layer.close(index);
                    });
                }
            });

            // 显示配置表单
            function showConfigForm(data) {
                var title = data ? '编辑配置' : '新增配置';
                var isEdit = !!data;
                
                layer.open({
                    type: 1,
                    title: title,
                    content: $('#configForm'),
                    area: ['600px', '700px'],
                    success: function(layero, index) {
                        // 重置表单
                        form.val('configFormFilter', {});
                        
                        if (isEdit) {
                            // 编辑模式，填充数据
                            form.val('configFormFilter', data);
                            
                            // 根据类型显示对应的输入控件
                            switchConfigValueInput(data.config_type);
                            
                            // 系统配置的键名不允许修改
                            if (data.is_system) {
                                $('input[name="config_key"]').prop('readonly', true);
                            } else {
                                $('input[name="config_key"]').prop('readonly', false);
                            }
                        } else {
                            // 新增模式
                            $('input[name="config_key"]').prop('readonly', false);
                            switchConfigValueInput('string');
                        }
                        
                        form.render();
                    }
                });
            }

            // 配置类型切换
            form.on('select(configType)', function(data) {
                switchConfigValueInput(data.value);
            });

            // 切换配置值输入控件
            function switchConfigValueInput(type) {
                $('#configValueInput, #configValueJson, #configValueBool').hide();
                
                switch(type) {
                    case 'json':
                        $('#configValueJson').show();
                        break;
                    case 'boolean':
                        $('#configValueBool').show();
                        break;
                    default:
                        $('#configValueInput').show();
                        break;
                }
                
                form.render();
            }

            // 自定义验证规则
            form.verify({
                configKey: function(value) {
                    if (!/^[a-zA-Z][a-zA-Z0-9_]*$/.test(value)) {
                        return '配置键名格式不正确，只能包含字母、数字和下划线，且必须以字母开头';
                    }
                }
            });

            // 表单提交
            form.on('submit(configSubmit)', function(data) {
                var formData = data.field;
                var isEdit = !!formData.id;
                
                // 处理配置值
                if (formData.config_type === 'json') {
                    formData.config_value = formData.config_value_json;
                } else if (formData.config_type === 'boolean') {
                    formData.config_value = formData.config_value_bool;
                }
                
                // 删除多余字段
                delete formData.config_value_json;
                delete formData.config_value_bool;
                
                var url = isEdit ? '/api/admin/portal/configs/' + formData.id : '/api/admin/portal/configs/';
                var method = isEdit ? 'PUT' : 'POST';
                
                $.ajax({
                    url: url,
                    method: method,
                    headers: {
                        'Authorization': 'Bearer ' + (localStorage.getItem('admin_token') || ''),
                        'Content-Type': 'application/json'
                    },
                    data: JSON.stringify(formData),
                    success: function(res) {
                        if (res.code === 200) {
                            layer.msg(isEdit ? '更新成功' : '创建成功');
                            layer.closeAll();
                            configTable.reload();
                            loadConfigGroups(); // 重新加载分组
                        } else {
                            layer.msg(res.message || (isEdit ? '更新失败' : '创建失败'));
                        }
                    },
                    error: function() {
                        layer.msg('请求失败');
                    }
                });
                
                return false;
            });

            // 取消按钮
            $('#cancelBtn').on('click', function() {
                layer.closeAll();
            });
        });
    </script>
</body>
</html>
