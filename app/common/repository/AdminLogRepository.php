<?php
declare(strict_types=1);

namespace app\common\repository;

use app\common\bean\AdminLogBean;
use orm\EntityManager;

/**
 * 管理员操作日志Repository
 */
class AdminLogRepository
{
    /**
     * 记录操作日志
     */
    public function record(int $userId, string $username, string $action, string $description = '', string $ip = '', string $userAgent = ''): bool
    {
        $log = new AdminLogBean();
        $log->userId = $userId;
        $log->username = $username;
        $log->action = $action;
        $log->description = $description;
        $log->ip = $ip ?: request()->ip();
        $log->userAgent = $userAgent ?: request()->header('user-agent', '');
        $log->createTime = date('Y-m-d H:i:s');
        
        $id = EntityManager::create($log)->insertBeanGetId($log);
        $log->id = $id;
        $log->stored();
        
        return $id > 0;
    }

    /**
     * 根据ID查找日志
     */
    public function findById(int $id): ?AdminLogBean
    {
        $bean = new AdminLogBean();
        $result = EntityManager::create($bean)
            ->where('id', $id)
            ->findResult();
        
        return $result && !$result->isEmpty() ? $result : null;
    }

    /**
     * 获取日志列表
     */
    public function getList(int $page = 1, int $limit = 15, array $where = []): array
    {
        $bean = new AdminLogBean();
        $query = EntityManager::create($bean);
        
        // 添加查询条件
        if (!empty($where['user_id'])) {
            $query->where('user_id', $where['user_id']);
        }
        if (!empty($where['username'])) {
            $query->where('username', 'like', '%' . $where['username'] . '%');
        }
        if (!empty($where['action'])) {
            $query->where('action', $where['action']);
        }
        if (!empty($where['ip'])) {
            $query->where('ip', 'like', '%' . $where['ip'] . '%');
        }
        if (!empty($where['start_time'])) {
            $query->where('create_time', '>=', $where['start_time']);
        }
        if (!empty($where['end_time'])) {
            $query->where('create_time', '<=', $where['end_time']);
        }
        
        // 分页
        $offset = ($page - 1) * $limit;
        $list = $query->limit($offset, $limit)
            ->order('id', 'desc')
            ->selectResult();
        
        // 获取总数
        $total = EntityManager::create($bean);
        if (!empty($where['user_id'])) {
            $total->where('user_id', $where['user_id']);
        }
        if (!empty($where['username'])) {
            $total->where('username', 'like', '%' . $where['username'] . '%');
        }
        if (!empty($where['action'])) {
            $total->where('action', $where['action']);
        }
        if (!empty($where['ip'])) {
            $total->where('ip', 'like', '%' . $where['ip'] . '%');
        }
        if (!empty($where['start_time'])) {
            $total->where('create_time', '>=', $where['start_time']);
        }
        if (!empty($where['end_time'])) {
            $total->where('create_time', '<=', $where['end_time']);
        }
        $totalCount = $total->count();
        
        return [
            'list' => $list,
            'total' => $totalCount,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($totalCount / $limit)
        ];
    }

    /**
     * 根据用户ID获取日志列表
     */
    public function getByUserId(int $userId, int $page = 1, int $limit = 15): array
    {
        return $this->getList($page, $limit, ['user_id' => $userId]);
    }

    /**
     * 根据操作类型获取日志列表
     */
    public function getByAction(string $action, int $page = 1, int $limit = 15): array
    {
        return $this->getList($page, $limit, ['action' => $action]);
    }

    /**
     * 删除指定时间之前的日志
     */
    public function deleteBeforeTime(string $time): int
    {
        $bean = new AdminLogBean();
        return EntityManager::create($bean)
            ->where('create_time', '<', $time)
            ->delete();
    }

    /**
     * 获取操作统计
     */
    public function getActionStats(string $startTime = '', string $endTime = ''): array
    {
        $bean = new AdminLogBean();
        $query = EntityManager::create($bean);
        
        if ($startTime) {
            $query->where('create_time', '>=', $startTime);
        }
        if ($endTime) {
            $query->where('create_time', '<=', $endTime);
        }
        
        // 这里需要使用原生SQL来实现分组统计
        // 由于EntityManager可能不直接支持复杂的分组查询，我们使用Db门面
        $sql = "SELECT action, COUNT(*) as count FROM admin_logs";
        $conditions = [];
        $params = [];
        
        if ($startTime) {
            $conditions[] = "create_time >= ?";
            $params[] = $startTime;
        }
        if ($endTime) {
            $conditions[] = "create_time <= ?";
            $params[] = $endTime;
        }
        
        if (!empty($conditions)) {
            $sql .= " WHERE " . implode(' AND ', $conditions);
        }
        
        $sql .= " GROUP BY action ORDER BY count DESC";
        
        return \think\facade\Db::query($sql, $params);
    }
}
