<?php
declare(strict_types=1);

namespace app\common\repository;

/**
 * UserRepository
 * 
 * 临时Repository类，用于测试
 */
class UserRepository
{
    /**
     * 获取列表
     */
    public function getList(int $page = 1, int $limit = 15, array $where = []): array
    {
        return [
            'data' => [],
            'total' => 0,
            'page' => $page,
            'limit' => $limit
        ];
    }
    
    /**
     * 根据ID获取
     */
    public function getById(int $id): ?array
    {
        return null;
    }
    
    /**
     * 创建
     */
    public function create(array $data): int
    {
        return 1;
    }
    
    /**
     * 更新
     */
    public function update(int $id, array $data): bool
    {
        return true;
    }
    
    /**
     * 删除
     */
    public function delete(int $id): bool
    {
        return true;
    }
}
