<?php
declare(strict_types=1);

namespace app\common\repository;

use app\common\bean\ArticleCustomFieldBean;
use orm\EntityManager;
use think\exception\ValidateException;

/**
 * 文章自定义字段Repository
 */
class ArticleCustomFieldRepository
{
    /**
     * 根据ID查找字段
     */
    public function findById(int $id): ?ArticleCustomFieldBean
    {
        $bean = new ArticleCustomFieldBean();
        $result = EntityManager::create($bean)
            ->where('id', $id)
            ->findResult();
        
        return $result && !$result->isEmpty() ? $result : null;
    }

    /**
     * 根据字段键名查找字段
     */
    public function findByFieldKey(string $fieldKey): ?ArticleCustomFieldBean
    {
        $bean = new ArticleCustomFieldBean();
        $result = EntityManager::create($bean)
            ->where('field_key', $fieldKey)
            ->findResult();
        
        return $result && !$result->isEmpty() ? $result : null;
    }

    /**
     * 获取所有启用的字段
     */
    public function findActive(): array
    {
        $bean = new ArticleCustomFieldBean();
        return EntityManager::create($bean)
            ->where('is_active', ArticleCustomFieldBean::ACTIVE_YES)
            ->order('sort_order', 'desc')
            ->order('id', 'asc')
            ->selectResult();
    }

    /**
     * 获取所有字段（包括禁用的）
     */
    public function findAll(): array
    {
        $bean = new ArticleCustomFieldBean();
        return EntityManager::create($bean)
            ->order('sort_order', 'desc')
            ->order('id', 'asc')
            ->selectResult();
    }

    /**
     * 分页获取字段列表
     */
    public function findWithPagination(array $where = [], int $page = 1, int $limit = 15): array
    {
        $bean = new ArticleCustomFieldBean();
        $query = EntityManager::create($bean);
        
        // 构建查询条件
        foreach ($where as $field => $value) {
            if (!is_null($value) && $value !== '') {
                switch ($field) {
                    case 'name':
                        $query->whereLike('name', '%' . $value . '%');
                        break;
                    case 'field_key':
                        $query->whereLike('field_key', '%' . $value . '%');
                        break;
                    case 'description':
                        $query->whereLike('description', '%' . $value . '%');
                        break;
                    default:
                        $query->where($field, $value);
                        break;
                }
            }
        }
        
        // 计算总数
        $total = $query->count();
        
        // 获取分页数据
        $offset = ($page - 1) * $limit;
        $list = $query->order('sort_order', 'desc')
            ->order('id', 'asc')
            ->limit($offset, $limit)
            ->selectResult();
        
        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ];
    }

    /**
     * 根据字段类型获取字段
     */
    public function findByType(string $fieldType, bool $onlyActive = true): array
    {
        $bean = new ArticleCustomFieldBean();
        $query = EntityManager::create($bean)
            ->where('field_type', $fieldType);
        
        if ($onlyActive) {
            $query->where('is_active', ArticleCustomFieldBean::ACTIVE_YES);
        }
        
        return $query->order('sort_order', 'desc')
            ->order('id', 'asc')
            ->selectResult();
    }

    /**
     * 获取必填字段
     */
    public function findRequired(bool $onlyActive = true): array
    {
        $bean = new ArticleCustomFieldBean();
        $query = EntityManager::create($bean)
            ->where('is_required', ArticleCustomFieldBean::REQUIRED_YES);
        
        if ($onlyActive) {
            $query->where('is_active', ArticleCustomFieldBean::ACTIVE_YES);
        }
        
        return $query->order('sort_order', 'desc')
            ->order('id', 'asc')
            ->selectResult();
    }

    /**
     * 创建字段
     */
    public function create(ArticleCustomFieldBean $field): int
    {
        // 设置创建时间
        $field->createTime = date('Y-m-d H:i:s');
        $field->updateTime = date('Y-m-d H:i:s');
        
        $id = EntityManager::create($field)->insertBeanGetId($field);
        $field->id = $id;
        $field->stored();
        
        return $id;
    }

    /**
     * 更新字段
     */
    public function update(ArticleCustomFieldBean $field): bool
    {
        if ($field->isEmpty() || !$field->id) {
            throw new ValidateException('字段不存在或ID为空');
        }
        
        // 设置更新时间
        $field->updateTime = date('Y-m-d H:i:s');
        
        $result = EntityManager::create($field)
            ->where('id', $field->id)
            ->updateBean($field);
        
        return $result > 0;
    }

    /**
     * 删除字段
     */
    public function delete(int $id): bool
    {
        $bean = new ArticleCustomFieldBean();
        $result = EntityManager::create($bean)
            ->where('id', $id)
            ->delete();
        
        return $result > 0;
    }

    /**
     * 切换启用状态
     */
    public function toggleActive(int $id): bool
    {
        $field = $this->findById($id);
        if (!$field) {
            throw new ValidateException('字段不存在');
        }
        
        $field->isActive = $field->isActive ? ArticleCustomFieldBean::ACTIVE_NO : ArticleCustomFieldBean::ACTIVE_YES;
        return $this->update($field);
    }

    /**
     * 批量更新排序
     */
    public function updateSort(array $sortData): bool
    {
        $bean = new ArticleCustomFieldBean();
        $manager = EntityManager::create($bean);
        
        foreach ($sortData as $item) {
            if (isset($item['id']) && isset($item['sort_order'])) {
                $manager->where('id', $item['id'])
                    ->update(['sort_order' => $item['sort_order'], 'update_time' => date('Y-m-d H:i:s')]);
            }
        }
        
        return true;
    }

    /**
     * 检查字段键名是否唯一
     */
    public function isFieldKeyUnique(string $fieldKey, int $excludeId = 0): bool
    {
        $bean = new ArticleCustomFieldBean();
        $query = EntityManager::create($bean)
            ->where('field_key', $fieldKey);
        
        if ($excludeId > 0) {
            $query->where('id', '!=', $excludeId);
        }
        
        return $query->count() === 0;
    }

    /**
     * 获取字段统计信息
     */
    public function getStatistics(): array
    {
        $bean = new ArticleCustomFieldBean();
        $manager = EntityManager::create($bean);
        
        return [
            'total' => $manager->count(),
            'active' => $manager->where('is_active', ArticleCustomFieldBean::ACTIVE_YES)->count(),
            'inactive' => $manager->where('is_active', ArticleCustomFieldBean::ACTIVE_NO)->count(),
            'required' => $manager->where('is_required', ArticleCustomFieldBean::REQUIRED_YES)->count(),
            'tag_type' => $manager->where('field_type', ArticleCustomFieldBean::TYPE_TAG)->count(),
            'text_type' => $manager->where('field_type', ArticleCustomFieldBean::TYPE_TEXT)->count(),
            'textarea_type' => $manager->where('field_type', ArticleCustomFieldBean::TYPE_TEXTAREA)->count()
        ];
    }

    /**
     * 根据多个ID获取字段
     */
    public function findByIds(array $ids): array
    {
        if (empty($ids)) {
            return [];
        }
        
        $bean = new ArticleCustomFieldBean();
        return EntityManager::create($bean)
            ->whereIn('id', $ids)
            ->order('sort_order', 'desc')
            ->order('id', 'asc')
            ->selectResult();
    }

    /**
     * 获取字段的键值对映射
     */
    public function getFieldKeyMap(bool $onlyActive = true): array
    {
        $fields = $onlyActive ? $this->findActive() : $this->findAll();
        $map = [];
        
        foreach ($fields as $field) {
            $map[$field->fieldKey] = $field;
        }
        
        return $map;
    }
}
