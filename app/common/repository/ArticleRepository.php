<?php
declare(strict_types=1);

namespace app\common\repository;

use app\common\bean\ArticleBean;
use orm\EntityManager;
use think\exception\ValidateException;

/**
 * 文章Repository
 */
class ArticleRepository
{
    /**
     * 根据ID查找文章
     */
    public function findById(int $id): ?ArticleBean
    {
        $bean = new ArticleBean();
        $result = EntityManager::create($bean)
            ->where('id', $id)
            ->findResult();
        
        return $result && !$result->isEmpty() ? $result : null;
    }

    /**
     * 根据slug查找文章
     */
    public function findBySlug(string $slug): ?ArticleBean
    {
        $bean = new ArticleBean();
        $result = EntityManager::create($bean)
            ->where('slug', $slug)
            ->findResult();
        
        return $result && !$result->isEmpty() ? $result : null;
    }

    /**
     * 根据分类ID获取文章列表
     */
    public function findByCategoryId(int $categoryId, bool $onlyPublished = false): array
    {
        $bean = new ArticleBean();
        $query = EntityManager::create($bean)
            ->where('category_id', $categoryId);
        
        if ($onlyPublished) {
            $query->where('status', ArticleBean::STATUS_PUBLISHED);
        }
        
        return $query->order('is_top', 'desc')
            ->order('sort_order', 'desc')
            ->order('publish_time', 'desc')
            ->order('id', 'desc')
            ->selectResult();
    }

    /**
     * 分页获取文章列表
     */
    public function findWithPagination(array $where = [], int $page = 1, int $limit = 15): array
    {
        $bean = new ArticleBean();
        $query = EntityManager::create($bean);
        
        // 构建查询条件
        foreach ($where as $field => $value) {
            if (!is_null($value) && $value !== '') {
                switch ($field) {
                    case 'title':
                        $query->whereLike('title', '%' . $value . '%');
                        break;
                    case 'author':
                        $query->whereLike('author', '%' . $value . '%');
                        break;
                    case 'content':
                        $query->whereLike('content', '%' . $value . '%');
                        break;
                    default:
                        $query->where($field, $value);
                        break;
                }
            }
        }
        
        // 计算总数
        $total = $query->count();
        
        // 获取分页数据
        $offset = ($page - 1) * $limit;
        $list = $query->order('is_top', 'desc')
            ->order('sort_order', 'desc')
            ->order('publish_time', 'desc')
            ->order('id', 'desc')
            ->limit($offset, $limit)
            ->selectResult();
        
        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ];
    }

    /**
     * 获取已发布的文章列表
     */
    public function findPublished(int $page = 1, int $limit = 15, array $where = []): array
    {
        $where['status'] = ArticleBean::STATUS_PUBLISHED;
        return $this->findWithPagination($where, $page, $limit);
    }

    /**
     * 获取置顶文章
     */
    public function findTopArticles(int $limit = 5): array
    {
        $bean = new ArticleBean();
        return EntityManager::create($bean)
            ->where('is_top', ArticleBean::TOP_YES)
            ->where('status', ArticleBean::STATUS_PUBLISHED)
            ->order('sort_order', 'desc')
            ->order('publish_time', 'desc')
            ->limit(0, $limit)
            ->selectResult();
    }

    /**
     * 创建文章
     */
    public function create(ArticleBean $article): int
    {
        // 设置创建时间
        $article->createTime = date('Y-m-d H:i:s');
        $article->updateTime = date('Y-m-d H:i:s');
        
        // 如果是发布状态且没有设置发布时间，则设置发布时间
        if ($article->status === ArticleBean::STATUS_PUBLISHED && empty($article->publishTime)) {
            $article->publishTime = date('Y-m-d H:i:s');
        }
        
        $id = EntityManager::create($article)->insertBeanGetId($article);
        $article->id = $id;
        $article->stored();
        
        return $id;
    }

    /**
     * 更新文章
     */
    public function update(ArticleBean $article): bool
    {
        if ($article->isEmpty() || !$article->id) {
            throw new ValidateException('文章不存在或ID为空');
        }
        
        // 设置更新时间
        $article->updateTime = date('Y-m-d H:i:s');
        
        // 获取原文章状态
        $oldArticle = $this->findById($article->id);
        if ($oldArticle) {
            // 如果状态从非发布变为发布，且没有设置发布时间，则设置发布时间
            if ($oldArticle->status !== ArticleBean::STATUS_PUBLISHED 
                && $article->status === ArticleBean::STATUS_PUBLISHED 
                && empty($article->publishTime)) {
                $article->publishTime = date('Y-m-d H:i:s');
            }
        }
        
        $result = EntityManager::create($article)
            ->where('id', $article->id)
            ->updateBean($article);
        
        return $result > 0;
    }

    /**
     * 删除文章
     */
    public function delete(int $id): bool
    {
        $bean = new ArticleBean();
        $result = EntityManager::create($bean)
            ->where('id', $id)
            ->delete();
        
        return $result > 0;
    }

    /**
     * 批量删除文章
     */
    public function batchDelete(array $ids): int
    {
        if (empty($ids)) {
            return 0;
        }
        
        $bean = new ArticleBean();
        return EntityManager::create($bean)
            ->whereIn('id', $ids)
            ->delete();
    }

    /**
     * 更新浏览次数
     */
    public function incrementViewCount(int $id): bool
    {
        $bean = new ArticleBean();
        $result = EntityManager::create($bean)
            ->where('id', $id)
            ->setInc('view_count', 1);
        
        return $result > 0;
    }

    /**
     * 切换置顶状态
     */
    public function toggleTop(int $id): bool
    {
        $article = $this->findById($id);
        if (!$article) {
            throw new ValidateException('文章不存在');
        }
        
        $article->isTop = $article->isTop ? ArticleBean::TOP_NO : ArticleBean::TOP_YES;
        return $this->update($article);
    }

    /**
     * 切换文章状态
     */
    public function changeStatus(int $id, string $status): bool
    {
        $article = $this->findById($id);
        if (!$article) {
            throw new ValidateException('文章不存在');
        }
        
        if (!in_array($status, [ArticleBean::STATUS_DRAFT, ArticleBean::STATUS_PUBLISHED, ArticleBean::STATUS_ARCHIVED])) {
            throw new ValidateException('无效的文章状态');
        }
        
        $article->status = $status;
        return $this->update($article);
    }

    /**
     * 批量更新排序
     */
    public function updateSort(array $sortData): bool
    {
        $bean = new ArticleBean();
        $manager = EntityManager::create($bean);
        
        foreach ($sortData as $item) {
            if (isset($item['id']) && isset($item['sort_order'])) {
                $manager->where('id', $item['id'])
                    ->update(['sort_order' => $item['sort_order'], 'update_time' => date('Y-m-d H:i:s')]);
            }
        }
        
        return true;
    }

    /**
     * 检查slug是否唯一
     */
    public function isSlugUnique(string $slug, int $excludeId = 0): bool
    {
        $bean = new ArticleBean();
        $query = EntityManager::create($bean)
            ->where('slug', $slug);
        
        if ($excludeId > 0) {
            $query->where('id', '!=', $excludeId);
        }
        
        return $query->count() === 0;
    }

    /**
     * 获取文章统计信息
     */
    public function getStatistics(): array
    {
        $bean = new ArticleBean();
        $manager = EntityManager::create($bean);
        
        return [
            'total' => $manager->count(),
            'published' => $manager->where('status', ArticleBean::STATUS_PUBLISHED)->count(),
            'draft' => $manager->where('status', ArticleBean::STATUS_DRAFT)->count(),
            'archived' => $manager->where('status', ArticleBean::STATUS_ARCHIVED)->count(),
            'top' => $manager->where('is_top', ArticleBean::TOP_YES)->count(),
            'total_views' => $manager->sum('view_count') ?: 0
        ];
    }

    /**
     * 获取轮播文章（有封面图的最新文章）
     */
    public function getBannerArticles(int $limit = 5): array
    {
        $bean = new ArticleBean();
        return EntityManager::create($bean)
            ->where('status', ArticleBean::STATUS_PUBLISHED)
            ->where('cover_image', '<>', '')
            ->where('cover_image', 'not null')
            ->order('publish_time', 'desc')
            ->limit($limit)
            ->selectResult();
    }

    /**
     * 获取热门文章（按浏览量排序）
     */
    public function getHotArticles(int $limit = 10): array
    {
        $bean = new ArticleBean();
        return EntityManager::create($bean)
            ->where('status', ArticleBean::STATUS_PUBLISHED)
            ->order('view_count', 'desc')
            ->limit($limit)
            ->selectResult();
    }

    /**
     * 获取最新文章
     */
    public function getLatestArticles(int $limit = 12): array
    {
        $bean = new ArticleBean();
        return EntityManager::create($bean)
            ->where('status', ArticleBean::STATUS_PUBLISHED)
            ->order('publish_time', 'desc')
            ->limit($limit)
            ->selectResult();
    }

    /**
     * 根据分类ID获取相关文章
     */
    public function getRelatedArticles(int $categoryId, int $excludeId = 0, int $limit = 6): array
    {
        $bean = new ArticleBean();
        $query = EntityManager::create($bean)
            ->where('category_id', $categoryId)
            ->where('status', ArticleBean::STATUS_PUBLISHED);

        if ($excludeId > 0) {
            $query->where('id', '<>', $excludeId);
        }

        return $query->order('publish_time', 'desc')
            ->limit($limit)
            ->selectResult();
    }
}
