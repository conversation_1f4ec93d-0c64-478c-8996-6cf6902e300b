<?php
declare(strict_types=1);

namespace app\common\repository;

use app\common\bean\ArticleCustomFieldValueBean;
use orm\EntityManager;
use think\exception\ValidateException;

/**
 * 文章自定义字段值Repository
 */
class ArticleCustomFieldValueRepository
{
    /**
     * 根据ID查找字段值
     */
    public function findById(int $id): ?ArticleCustomFieldValueBean
    {
        $bean = new ArticleCustomFieldValueBean();
        $result = EntityManager::create($bean)
            ->where('id', $id)
            ->findResult();
        
        return $result && !$result->isEmpty() ? $result : null;
    }

    /**
     * 根据文章ID和字段ID查找字段值
     */
    public function findByArticleAndField(int $articleId, int $fieldId): ?ArticleCustomFieldValueBean
    {
        $bean = new ArticleCustomFieldValueBean();
        $result = EntityManager::create($bean)
            ->where('article_id', $articleId)
            ->where('field_id', $fieldId)
            ->findResult();
        
        return $result && !$result->isEmpty() ? $result : null;
    }

    /**
     * 根据文章ID获取所有字段值
     */
    public function findByArticleId(int $articleId): array
    {
        $bean = new ArticleCustomFieldValueBean();
        return EntityManager::create($bean)
            ->where('article_id', $articleId)
            ->selectResult();
    }

    /**
     * 根据字段ID获取所有字段值
     */
    public function findByFieldId(int $fieldId): array
    {
        $bean = new ArticleCustomFieldValueBean();
        return EntityManager::create($bean)
            ->where('field_id', $fieldId)
            ->selectResult();
    }

    /**
     * 获取文章的字段值映射（字段ID => 字段值）
     */
    public function getArticleFieldMap(int $articleId): array
    {
        $values = $this->findByArticleId($articleId);
        $map = [];
        
        foreach ($values as $value) {
            $map[$value->fieldId] = $value;
        }
        
        return $map;
    }

    /**
     * 获取文章的字段值键值对（字段键名 => 字段值）
     */
    public function getArticleFieldKeyMap(int $articleId): array
    {
        $bean = new ArticleCustomFieldValueBean();
        $sql = "SELECT v.*, f.field_key, f.field_type 
                FROM article_custom_field_values v 
                LEFT JOIN article_custom_fields f ON v.field_id = f.id 
                WHERE v.article_id = ? AND f.is_active = 1";
        
        $results = EntityManager::create($bean)->query($sql, [$articleId]);
        $map = [];
        
        foreach ($results as $row) {
            $map[$row['field_key']] = [
                'value' => $row['field_value'],
                'field_type' => $row['field_type'],
                'field_id' => $row['field_id']
            ];
        }
        
        return $map;
    }

    /**
     * 创建字段值
     */
    public function create(ArticleCustomFieldValueBean $value): int
    {
        // 设置创建时间
        $value->createTime = date('Y-m-d H:i:s');
        $value->updateTime = date('Y-m-d H:i:s');
        
        $id = EntityManager::create($value)->insertBeanGetId($value);
        $value->id = $id;
        $value->stored();
        
        return $id;
    }

    /**
     * 更新字段值
     */
    public function update(ArticleCustomFieldValueBean $value): bool
    {
        if ($value->isEmpty() || !$value->id) {
            throw new ValidateException('字段值不存在或ID为空');
        }
        
        // 设置更新时间
        $value->updateTime = date('Y-m-d H:i:s');
        
        $result = EntityManager::create($value)
            ->where('id', $value->id)
            ->updateBean($value);
        
        return $result > 0;
    }

    /**
     * 删除字段值
     */
    public function delete(int $id): bool
    {
        $bean = new ArticleCustomFieldValueBean();
        $result = EntityManager::create($bean)
            ->where('id', $id)
            ->delete();
        
        return $result > 0;
    }

    /**
     * 删除文章的所有字段值
     */
    public function deleteByArticleId(int $articleId): int
    {
        $bean = new ArticleCustomFieldValueBean();
        return EntityManager::create($bean)
            ->where('article_id', $articleId)
            ->delete();
    }

    /**
     * 删除字段的所有值
     */
    public function deleteByFieldId(int $fieldId): int
    {
        $bean = new ArticleCustomFieldValueBean();
        return EntityManager::create($bean)
            ->where('field_id', $fieldId)
            ->delete();
    }

    /**
     * 保存或更新字段值
     */
    public function saveOrUpdate(int $articleId, int $fieldId, $fieldValue): bool
    {
        $existing = $this->findByArticleAndField($articleId, $fieldId);
        
        if ($existing) {
            // 如果字段值为空，删除记录
            if (is_null($fieldValue) || $fieldValue === '') {
                return $this->delete($existing->id);
            }
            
            // 更新现有记录
            $existing->fieldValue = is_array($fieldValue) ? json_encode($fieldValue, JSON_UNESCAPED_UNICODE) : (string)$fieldValue;
            return $this->update($existing);
        } else {
            // 如果字段值为空，不创建记录
            if (is_null($fieldValue) || $fieldValue === '') {
                return true;
            }
            
            // 创建新记录
            $value = new ArticleCustomFieldValueBean();
            $value->articleId = $articleId;
            $value->fieldId = $fieldId;
            $value->fieldValue = is_array($fieldValue) ? json_encode($fieldValue, JSON_UNESCAPED_UNICODE) : (string)$fieldValue;
            
            $this->create($value);
            return true;
        }
    }

    /**
     * 批量保存文章的字段值
     */
    public function batchSaveArticleFields(int $articleId, array $fieldValues): bool
    {
        foreach ($fieldValues as $fieldId => $fieldValue) {
            $this->saveOrUpdate($articleId, (int)$fieldId, $fieldValue);
        }
        
        return true;
    }

    /**
     * 批量保存文章的字段值（通过字段键名）
     */
    public function batchSaveArticleFieldsByKey(int $articleId, array $fieldKeyValues, array $fieldKeyMap): bool
    {
        foreach ($fieldKeyValues as $fieldKey => $fieldValue) {
            if (isset($fieldKeyMap[$fieldKey])) {
                $fieldId = $fieldKeyMap[$fieldKey]->id;
                $this->saveOrUpdate($articleId, $fieldId, $fieldValue);
            }
        }
        
        return true;
    }

    /**
     * 复制文章的字段值到另一篇文章
     */
    public function copyArticleFields(int $fromArticleId, int $toArticleId): bool
    {
        $values = $this->findByArticleId($fromArticleId);
        
        foreach ($values as $value) {
            $newValue = new ArticleCustomFieldValueBean();
            $newValue->articleId = $toArticleId;
            $newValue->fieldId = $value->fieldId;
            $newValue->fieldValue = $value->fieldValue;
            
            $this->create($newValue);
        }
        
        return true;
    }

    /**
     * 获取字段值统计信息
     */
    public function getStatistics(): array
    {
        $bean = new ArticleCustomFieldValueBean();
        $manager = EntityManager::create($bean);
        
        return [
            'total' => $manager->count(),
            'articles_with_custom_fields' => $manager->distinct('article_id')->count(),
            'fields_in_use' => $manager->distinct('field_id')->count()
        ];
    }

    /**
     * 搜索包含特定字段值的文章
     */
    public function searchByFieldValue(int $fieldId, string $searchValue): array
    {
        $bean = new ArticleCustomFieldValueBean();
        return EntityManager::create($bean)
            ->where('field_id', $fieldId)
            ->whereLike('field_value', '%' . $searchValue . '%')
            ->selectResult();
    }

    /**
     * 获取字段的使用统计
     */
    public function getFieldUsageStats(int $fieldId): array
    {
        $bean = new ArticleCustomFieldValueBean();
        $manager = EntityManager::create($bean);
        
        return [
            'total_usage' => $manager->where('field_id', $fieldId)->count(),
            'non_empty_usage' => $manager->where('field_id', $fieldId)
                ->where('field_value', '!=', '')
                ->whereNotNull('field_value')
                ->count()
        ];
    }

    /**
     * 获取文章的自定义字段值（用于前端显示）
     */
    public function getArticleCustomFieldsForDisplay(int $articleId): array
    {
        // 这里需要使用原生SQL或者更复杂的查询，暂时返回空数组
        // 在实际项目中，可以考虑使用Db facade或者创建专门的查询方法
        return [];
    }
}
