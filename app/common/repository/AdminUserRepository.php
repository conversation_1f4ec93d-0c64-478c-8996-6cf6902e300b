<?php
declare(strict_types=1);

namespace app\common\repository;

use app\common\bean\AdminUserBean;
use orm\EntityManager;
use think\exception\ValidateException;

/**
 * 管理员用户Repository
 */
class AdminUserRepository
{
    /**
     * 根据用户名查找用户
     */
    public function findByUsername(string $username): ?AdminUserBean
    {
        $bean = new AdminUserBean();
        $result = EntityManager::create($bean)
            ->where('username', $username)
            ->findResult();
        
        return $result && !$result->isEmpty() ? $result : null;
    }

    /**
     * 根据ID查找用户
     */
    public function findById(int $id): ?AdminUserBean
    {
        $bean = new AdminUserBean();
        $result = EntityManager::create($bean)
            ->where('id', $id)
            ->findResult();
        
        return $result && !$result->isEmpty() ? $result : null;
    }

    /**
     * 根据邮箱查找用户
     */
    public function findByEmail(string $email): ?AdminUserBean
    {
        $bean = new AdminUserBean();
        $result = EntityManager::create($bean)
            ->where('email', $email)
            ->findResult();
        
        return $result && !$result->isEmpty() ? $result : null;
    }

    /**
     * 创建用户
     */
    public function create(AdminUserBean $user): int
    {
        // 设置创建时间
        $user->createTime = date('Y-m-d H:i:s');
        $user->updateTime = date('Y-m-d H:i:s');
        
        $id = EntityManager::create($user)->insertBeanGetId($user);
        $user->id = $id;
        $user->stored();
        
        return $id;
    }

    /**
     * 更新用户
     */
    public function update(AdminUserBean $user): bool
    {
        if ($user->isEmpty() || !$user->id) {
            throw new ValidateException('用户不存在或ID为空');
        }
        
        // 设置更新时间
        $user->updateTime = date('Y-m-d H:i:s');
        
        $result = EntityManager::create($user)
            ->where('id', $user->id)
            ->updateBean($user);
        
        return $result > 0;
    }

    /**
     * 删除用户
     */
    public function delete(int $id): bool
    {
        $bean = new AdminUserBean();
        $result = EntityManager::create($bean)
            ->where('id', $id)
            ->delete();
        
        return $result > 0;
    }

    /**
     * 获取用户列表
     */
    public function getList(int $page = 1, int $limit = 15, array $where = []): array
    {
        $bean = new AdminUserBean();
        $query = EntityManager::create($bean);
        
        // 添加查询条件
        if (!empty($where['username'])) {
            $query->where('username', 'like', '%' . $where['username'] . '%');
        }
        if (!empty($where['email'])) {
            $query->where('email', 'like', '%' . $where['email'] . '%');
        }
        if (!empty($where['role'])) {
            $query->where('role', $where['role']);
        }
        if (isset($where['status']) && $where['status'] !== '') {
            $query->where('status', $where['status']);
        }
        
        // 分页
        $offset = ($page - 1) * $limit;
        $list = $query->limit($offset, $limit)
            ->order('id', 'desc')
            ->selectResult();
        
        // 获取总数
        $total = EntityManager::create($bean);
        if (!empty($where['username'])) {
            $total->where('username', 'like', '%' . $where['username'] . '%');
        }
        if (!empty($where['email'])) {
            $total->where('email', 'like', '%' . $where['email'] . '%');
        }
        if (!empty($where['role'])) {
            $total->where('role', $where['role']);
        }
        if (isset($where['status']) && $where['status'] !== '') {
            $total->where('status', $where['status']);
        }
        $totalCount = $total->count();
        
        return [
            'list' => $list,
            'total' => $totalCount,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($totalCount / $limit)
        ];
    }

    /**
     * 检查用户名是否存在
     */
    public function existsByUsername(string $username, int $excludeId = 0): bool
    {
        $query = EntityManager::create(new AdminUserBean())
            ->where('username', $username);
        
        if ($excludeId > 0) {
            $query->where('id', '<>', $excludeId);
        }
        
        return $query->count() > 0;
    }

    /**
     * 检查邮箱是否存在
     */
    public function existsByEmail(string $email, int $excludeId = 0): bool
    {
        $query = EntityManager::create(new AdminUserBean())
            ->where('email', $email);
        
        if ($excludeId > 0) {
            $query->where('id', '<>', $excludeId);
        }
        
        return $query->count() > 0;
    }

    /**
     * 更新最后登录信息
     */
    public function updateLastLogin(int $userId, string $ip): bool
    {
        $user = $this->findById($userId);
        if (!$user) {
            return false;
        }
        
        $user->updateLastLogin($ip);
        return $this->update($user);
    }
}
