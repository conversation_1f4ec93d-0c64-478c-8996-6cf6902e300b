<?php
declare(strict_types=1);

namespace app\common\bean;

use orm\annotation\Column;
use orm\annotation\Id;
use orm\annotation\Table;
use orm\BaseBean;

/**
 * 文章自定义字段值Bean
 * @Table(name="article_custom_field_values")
 */
class ArticleCustomFieldValueBean extends BaseBean
{
    /**
     * @Id(auto=true)
     * @Column(name="id", type="integer")
     */
    public mixed $id = null;

    /**
     * @Column(name="article_id", type="integer")
     */
    public mixed $articleId = null;

    /**
     * @Column(name="field_id", type="integer")
     */
    public mixed $fieldId = null;

    /**
     * @Column(name="field_value", type="string")
     */
    public mixed $fieldValue = null;

    /**
     * @Column(name="create_time", type="datetime")
     */
    public mixed $createTime = null;

    /**
     * @Column(name="update_time", type="datetime")
     */
    public mixed $updateTime = null;

    /**
     * 获取字段值（根据字段类型解析）
     */
    public function getParsedValue(string $fieldType = null)
    {
        if (empty($this->fieldValue)) {
            return null;
        }

        // 如果是标签类型，尝试解析JSON
        if ($fieldType === ArticleCustomFieldBean::TYPE_TAG) {
            $decoded = json_decode($this->fieldValue, true);
            return (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) ? $decoded : [];
        }

        // 其他类型直接返回字符串值
        return $this->fieldValue;
    }

    /**
     * 设置字段值（根据字段类型处理）
     */
    public function setParsedValue($value, string $fieldType = null): void
    {
        if (is_null($value)) {
            $this->fieldValue = null;
            return;
        }

        // 如果是标签类型且值是数组，转换为JSON
        if ($fieldType === ArticleCustomFieldBean::TYPE_TAG && is_array($value)) {
            $this->fieldValue = json_encode($value, JSON_UNESCAPED_UNICODE);
            return;
        }

        // 其他情况转换为字符串
        $this->fieldValue = (string)$value;
    }

    /**
     * 检查字段值是否为空
     */
    public function isEmpty(): bool
    {
        return is_null($this->fieldValue) || $this->fieldValue === '';
    }

    /**
     * 获取字段值的显示文本
     */
    public function getDisplayValue(string $fieldType = null): string
    {
        if ($this->isEmpty()) {
            return '';
        }

        $value = $this->getParsedValue($fieldType);

        // 标签类型显示为逗号分隔的字符串
        if ($fieldType === ArticleCustomFieldBean::TYPE_TAG && is_array($value)) {
            return implode(', ', $value);
        }

        // 其他类型直接返回字符串
        return (string)$value;
    }

    /**
     * 验证字段值格式
     */
    public function validateValue(string $fieldType): bool
    {
        if ($this->isEmpty()) {
            return true; // 空值认为是有效的
        }

        switch ($fieldType) {
            case ArticleCustomFieldBean::TYPE_TAG:
                // 标签类型必须是有效的JSON数组
                $decoded = json_decode($this->fieldValue, true);
                return json_last_error() === JSON_ERROR_NONE && is_array($decoded);
                
            case ArticleCustomFieldBean::TYPE_TEXT:
            case ArticleCustomFieldBean::TYPE_TEXTAREA:
                // 文本类型必须是字符串
                return is_string($this->fieldValue);
                
            default:
                return false;
        }
    }

    /**
     * 获取字段值的字节长度
     */
    public function getValueLength(): int
    {
        return mb_strlen($this->fieldValue ?? '');
    }

    /**
     * 截取字段值
     */
    public function truncateValue(int $length, string $suffix = '...'): string
    {
        if ($this->isEmpty()) {
            return '';
        }

        $value = $this->fieldValue;
        if (mb_strlen($value) <= $length) {
            return $value;
        }

        return mb_substr($value, 0, $length) . $suffix;
    }

    /**
     * 转换为数组
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'article_id' => $this->articleId,
            'field_id' => $this->fieldId,
            'field_value' => $this->fieldValue,
            'create_time' => $this->createTime,
            'update_time' => $this->updateTime,
            'is_empty' => $this->isEmpty(),
            'value_length' => $this->getValueLength()
        ];
    }

    /**
     * 转换为包含解析值的数组
     */
    public function toArrayWithParsed(string $fieldType = null): array
    {
        $array = $this->toArray();
        $array['parsed_value'] = $this->getParsedValue($fieldType);
        $array['display_value'] = $this->getDisplayValue($fieldType);
        return $array;
    }
}
