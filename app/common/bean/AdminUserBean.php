<?php
declare(strict_types=1);

namespace app\common\bean;

use orm\BaseBean;
use orm\mapping\Table;
use orm\mapping\Column;
use orm\mapping\Id;

/**
 * 管理员用户Bean
 * @Table(name="admin_users")
 */
class AdminUserBean extends BaseBean
{
    /**
     * @Id(auto=true)
     * @Column(name="id", type="integer")
     */
    public mixed $id = null;

    /**
     * @Column(name="username", type="string")
     */
    public mixed $username = null;

    /**
     * @Column(name="password", type="string")
     */
    public mixed $password = null;

    /**
     * @Column(name="email", type="string")
     */
    public mixed $email = null;

    /**
     * @Column(name="real_name", type="string")
     */
    public mixed $realName = null;

    /**
     * @Column(name="avatar", type="string")
     */
    public mixed $avatar = null;

    /**
     * @Column(name="role", type="string")
     */
    public mixed $role = null;

    /**
     * @Column(name="status", type="integer")
     */
    public mixed $status = null;

    /**
     * @Column(name="last_login_time", type="datetime")
     */
    public mixed $lastLoginTime = null;

    /**
     * @Column(name="last_login_ip", type="string")
     */
    public mixed $lastLoginIp = null;

    /**
     * @Column(name="create_time", type="datetime")
     */
    public mixed $createTime = null;

    /**
     * @Column(name="update_time", type="datetime")
     */
    public mixed $updateTime = null;

    // 角色常量
    const ROLE_SUPER_ADMIN = 'super_admin';
    const ROLE_CONTENT_ADMIN = 'content_admin';
    
    // 状态常量
    const STATUS_DISABLED = 0;
    const STATUS_ENABLED = 1;

    /**
     * 验证密码
     */
    public function checkPassword(string $password): bool
    {
        return password_verify($password, $this->password);
    }

    /**
     * 设置密码（加密）
     */
    public function setPassword(string $password): void
    {
        $this->password = password_hash($password, PASSWORD_DEFAULT);
    }

    /**
     * 是否为超级管理员
     */
    public function isSuperAdmin(): bool
    {
        return $this->role === self::ROLE_SUPER_ADMIN;
    }

    /**
     * 是否为内容管理员
     */
    public function isContentAdmin(): bool
    {
        return $this->role === self::ROLE_CONTENT_ADMIN;
    }

    /**
     * 检查是否有指定角色
     */
    public function hasRole(string $role): bool
    {
        return $this->role === $role;
    }

    /**
     * 检查是否有指定角色之一
     */
    public function hasAnyRole(array $roles): bool
    {
        return in_array($this->role, $roles);
    }

    /**
     * 获取角色文本
     */
    public function getRoleText(): string
    {
        $roleList = [
            self::ROLE_SUPER_ADMIN => '超级管理员',
            self::ROLE_CONTENT_ADMIN => '内容管理员',
        ];
        return $roleList[$this->role] ?? '未知';
    }

    /**
     * 获取状态文本
     */
    public function getStatusText(): string
    {
        $statusList = [
            self::STATUS_DISABLED => '禁用',
            self::STATUS_ENABLED => '启用',
        ];
        return $statusList[$this->status] ?? '未知';
    }

    /**
     * 更新最后登录信息
     */
    public function updateLastLogin(string $ip): void
    {
        $this->lastLoginTime = date('Y-m-d H:i:s');
        $this->lastLoginIp = $ip;
    }

    /**
     * 转换为数组（隐藏密码）
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'username' => $this->username,
            'email' => $this->email,
            'real_name' => $this->realName,
            'avatar' => $this->avatar,
            'role' => $this->role,
            'role_text' => $this->getRoleText(),
            'status' => $this->status,
            'status_text' => $this->getStatusText(),
            'last_login_time' => $this->lastLoginTime,
            'last_login_ip' => $this->lastLoginIp,
            'create_time' => $this->createTime,
            'update_time' => $this->updateTime,
        ];
    }
}
