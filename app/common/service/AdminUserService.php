<?php
declare(strict_types=1);

namespace app\common\service;

use app\common\bean\AdminUserBean;
use app\common\repository\AdminUserRepository;
use app\api\repository\AdminLogRepository;
use think\exception\ValidateException;

/**
 * 管理员用户服务
 */
class AdminUserService
{
    protected AdminUserRepository $userRepository;
    protected AdminLogRepository $logRepository;
    protected AdminAuthService $authService;
    
    public function __construct()
    {
        $this->userRepository = new AdminUserRepository();
        $this->logRepository = new AdminLogRepository();
        $this->authService = new AdminAuthService();
    }
    
    /**
     * 获取用户列表
     */
    public function getList(int $page = 1, int $limit = 15, array $where = []): array
    {
        return $this->userRepository->getList($page, $limit, $where);
    }
    
    /**
     * 根据ID获取用户详情
     */
    public function getById(int $id): ?AdminUserBean
    {
        return $this->userRepository->findById($id);
    }
    
    /**
     * 创建用户
     */
    public function create(array $data): AdminUserBean
    {
        // 数据验证
        $this->validateUserData($data);
        
        // 检查用户名是否存在
        if ($this->userRepository->existsByUsername($data['username'])) {
            throw new ValidateException('用户名已存在');
        }
        
        // 检查邮箱是否存在
        if (!empty($data['email']) && $this->userRepository->existsByEmail($data['email'])) {
            throw new ValidateException('邮箱已存在');
        }
        
        // 创建用户Bean
        $user = new AdminUserBean();
        $user->username = $data['username'];
        $user->setPassword($data['password']);
        $user->email = $data['email'] ?? '';
        $user->realName = $data['real_name'] ?? '';
        $user->avatar = $data['avatar'] ?? '';
        $user->role = $data['role'] ?? AdminUserBean::ROLE_CONTENT_ADMIN;
        $user->status = $data['status'] ?? AdminUserBean::STATUS_ENABLED;
        
        // 保存到数据库
        $this->userRepository->create($user);
        
        // 记录操作日志
        $currentUser = $this->authService->getUser();
        if ($currentUser) {
            $this->logRepository->record(
                $currentUser['id'],
                $currentUser['username'],
                'create_user',
                "创建用户：{$user->username}"
            );
        }
        
        return $user;
    }
    
    /**
     * 更新用户
     */
    public function update(int $id, array $data): AdminUserBean
    {
        $user = $this->userRepository->findById($id);
        if (!$user) {
            throw new ValidateException('用户不存在');
        }
        
        // 数据验证（更新时密码可选）
        $this->validateUserData($data, false, $id);
        
        // 检查用户名是否存在（排除当前用户）
        if (isset($data['username']) && $this->userRepository->existsByUsername($data['username'], $id)) {
            throw new ValidateException('用户名已存在');
        }
        
        // 检查邮箱是否存在（排除当前用户）
        if (isset($data['email']) && !empty($data['email']) && $this->userRepository->existsByEmail($data['email'], $id)) {
            throw new ValidateException('邮箱已存在');
        }
        
        // 更新用户信息
        if (isset($data['username'])) $user->username = $data['username'];
        if (isset($data['password']) && !empty($data['password'])) $user->setPassword($data['password']);
        if (isset($data['email'])) $user->email = $data['email'];
        if (isset($data['real_name'])) $user->realName = $data['real_name'];
        if (isset($data['avatar'])) $user->avatar = $data['avatar'];
        if (isset($data['role'])) $user->role = $data['role'];
        if (isset($data['status'])) $user->status = $data['status'];
        
        // 保存到数据库
        $this->userRepository->update($user);
        
        // 记录操作日志
        $currentUser = $this->authService->getUser();
        if ($currentUser) {
            $this->logRepository->record(
                $currentUser['id'],
                $currentUser['username'],
                'update_user',
                "更新用户：{$user->username}"
            );
        }
        
        return $user;
    }
    
    /**
     * 删除用户
     */
    public function delete(int $id): bool
    {
        $user = $this->userRepository->findById($id);
        if (!$user) {
            throw new ValidateException('用户不存在');
        }
        
        // 不能删除自己
        $currentUserId = $this->authService->getUserId();
        if ($id === $currentUserId) {
            throw new ValidateException('不能删除自己');
        }
        
        // 删除用户
        $result = $this->userRepository->delete($id);
        
        if ($result) {
            // 记录操作日志
            $currentUser = $this->authService->getUser();
            if ($currentUser) {
                $this->logRepository->record(
                    $currentUser['id'],
                    $currentUser['username'],
                    'delete_user',
                    "删除用户：{$user->username}"
                );
            }
        }
        
        return $result;
    }
    
    /**
     * 修改密码
     */
    public function changePassword(int $id, string $oldPassword, string $newPassword): bool
    {
        $user = $this->userRepository->findById($id);
        if (!$user) {
            throw new ValidateException('用户不存在');
        }
        
        // 验证旧密码
        if (!$user->checkPassword($oldPassword)) {
            throw new ValidateException('原密码错误');
        }
        
        // 设置新密码
        $user->setPassword($newPassword);
        $result = $this->userRepository->update($user);
        
        if ($result) {
            // 记录操作日志
            $this->logRepository->record(
                $user->id,
                $user->username,
                'change_password',
                '修改密码'
            );
        }
        
        return $result;
    }
    
    /**
     * 验证用户数据
     */
    private function validateUserData(array $data, bool $requirePassword = true, int $excludeId = 0): void
    {
        // 用户名验证（更新时可选）
        if (isset($data['username']) && empty($data['username'])) {
            throw new ValidateException('用户名不能为空');
        }
        
        if (isset($data['username'])) {
            if (strlen($data['username']) < 3 || strlen($data['username']) > 50) {
                throw new ValidateException('用户名长度必须在3-50个字符之间');
            }

            if (!preg_match('/^[a-zA-Z0-9_]+$/', $data['username'])) {
                throw new ValidateException('用户名只能包含字母、数字和下划线');
            }
        }
        
        // 密码验证
        if ($requirePassword) {
            if (empty($data['password'])) {
                throw new ValidateException('密码不能为空');
            }

            if (strlen($data['password']) < 6) {
                throw new ValidateException('密码长度不能少于6位');
            }
        } elseif (isset($data['password']) && !empty($data['password'])) {
            // 更新时如果提供了密码，也要验证长度
            if (strlen($data['password']) < 6) {
                throw new ValidateException('密码长度不能少于6位');
            }
        }
        
        // 邮箱验证
        if (!empty($data['email']) && !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            throw new ValidateException('邮箱格式不正确');
        }
        
        // 角色验证
        if (isset($data['role'])) {
            $validRoles = [AdminUserBean::ROLE_SUPER_ADMIN, AdminUserBean::ROLE_CONTENT_ADMIN];
            if (!in_array($data['role'], $validRoles)) {
                throw new ValidateException('角色参数无效');
            }
        }
        
        // 状态验证
        if (isset($data['status'])) {
            $validStatuses = [AdminUserBean::STATUS_DISABLED, AdminUserBean::STATUS_ENABLED];
            if (!in_array($data['status'], $validStatuses)) {
                throw new ValidateException('状态参数无效');
            }
        }
    }
}
