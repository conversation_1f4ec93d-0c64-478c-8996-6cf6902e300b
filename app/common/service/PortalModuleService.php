<?php

namespace app\common\service;

use app\common\model\PortalModule;
use think\facade\Cache;
use think\exception\ValidateException;

/**
 * 门户模块服务类
 */
class PortalModuleService
{
    // 缓存前缀
    const CACHE_PREFIX = 'portal_module:';
    
    // 缓存过期时间（秒）
    const CACHE_EXPIRE = 3600;
    
    /**
     * 获取模块列表（分页）
     *
     * @param int $page 页码
     * @param int $limit 每页数量
     * @param string $keyword 关键词搜索
     * @param bool $onlyEnabled 仅显示启用的模块
     * @return array
     */
    public function getModuleList(int $page = 1, int $limit = 20, string $keyword = '', bool $onlyEnabled = false): array
    {
        $query = PortalModule::order('sort_order,id');
        
        // 仅显示启用的模块
        if ($onlyEnabled) {
            $query->where('is_enabled', 1);
        }
        
        // 关键词搜索
        if (!empty($keyword)) {
            $query->where(function($q) use ($keyword) {
                $q->whereLike('module_name', "%{$keyword}%")
                  ->whereOr('module_title', 'like', "%{$keyword}%")
                  ->whereOr('module_description', 'like', "%{$keyword}%");
            });
        }
        
        $result = $query->paginate([
            'list_rows' => $limit,
            'page' => $page,
        ]);
        
        return [
            'list' => $result->items(),
            'total' => $result->total(),
            'page' => $page,
            'limit' => $limit,
        ];
    }
    
    /**
     * 获取所有启用的模块（用于前端展示）
     *
     * @param bool $useCache 是否使用缓存
     * @return array
     */
    public function getEnabledModules(bool $useCache = true): array
    {
        if ($useCache) {
            $cacheKey = self::CACHE_PREFIX . 'enabled';
            $modules = Cache::get($cacheKey);

            if ($modules !== false && is_array($modules)) {
                return $modules;
            }
        }

        $modules = PortalModule::getEnabledModules();

        // 确保返回数组类型
        if (!is_array($modules)) {
            $modules = [];
        }

        if ($useCache) {
            Cache::set($cacheKey, $modules, self::CACHE_EXPIRE);
        }

        return $modules;
    }
    
    /**
     * 根据ID获取模块
     *
     * @param int $id 模块ID
     * @return PortalModule|null
     */
    public function getModuleById(int $id): ?PortalModule
    {
        return PortalModule::find($id);
    }
    
    /**
     * 根据模块名获取模块
     *
     * @param string $moduleName 模块名称
     * @param bool $useCache 是否使用缓存
     * @return PortalModule|null
     */
    public function getModuleByName(string $moduleName, bool $useCache = true): ?PortalModule
    {
        if ($useCache) {
            $cacheKey = self::CACHE_PREFIX . 'name:' . $moduleName;
            $module = Cache::get($cacheKey);
            
            if ($module !== false) {
                return $module;
            }
        }
        
        $module = PortalModule::getByName($moduleName);
        
        if ($useCache && $module) {
            Cache::set($cacheKey, $module, self::CACHE_EXPIRE);
        }
        
        return $module;
    }
    
    /**
     * 创建模块
     *
     * @param array $data 模块数据
     * @return PortalModule
     * @throws ValidateException
     */
    public function createModule(array $data): PortalModule
    {
        // 数据验证
        $this->validateModuleData($data);
        
        // 检查模块名是否已存在
        if (PortalModule::where('module_name', $data['module_name'])->find()) {
            throw new ValidateException('模块名称已存在');
        }
        
        // 设置默认值
        $data['column_count'] = $data['column_count'] ?? 1;
        $data['is_enabled'] = $data['is_enabled'] ?? true;
        $data['sort_order'] = $data['sort_order'] ?? $this->getNextSortOrder();
        $data['config_data'] = $data['config_data'] ?? [];
        
        $module = PortalModule::create($data);
        
        // 清除相关缓存
        $this->clearModuleCache();
        
        return $module;
    }
    
    /**
     * 更新模块
     *
     * @param int $id 模块ID
     * @param array $data 模块数据
     * @return PortalModule
     * @throws ValidateException
     */
    public function updateModule(int $id, array $data): PortalModule
    {
        $module = PortalModule::find($id);
        if (!$module) {
            throw new ValidateException('模块不存在');
        }
        
        // 验证数据
        $this->validateModuleData($data, $id);
        
        // 检查模块名是否已存在（排除当前记录）
        if (isset($data['module_name']) && $data['module_name'] !== $module->module_name) {
            if (PortalModule::where('module_name', $data['module_name'])->where('id', '<>', $id)->find()) {
                throw new ValidateException('模块名称已存在');
            }
        }
        
        $oldName = $module->module_name;
        
        $module->save($data);
        
        // 清除相关缓存
        $this->clearModuleCache($oldName);
        if (isset($data['module_name']) && $data['module_name'] !== $oldName) {
            $this->clearModuleCache($data['module_name']);
        }
        
        return $module;
    }
    
    /**
     * 删除模块
     *
     * @param int $id 模块ID
     * @return bool
     * @throws ValidateException
     */
    public function deleteModule(int $id): bool
    {
        $module = PortalModule::find($id);
        if (!$module) {
            throw new ValidateException('模块不存在');
        }
        
        $moduleName = $module->module_name;
        
        $result = $module->delete();
        
        if ($result) {
            // 清除相关缓存
            $this->clearModuleCache($moduleName);
        }
        
        return $result;
    }
    
    /**
     * 切换模块启用状态
     *
     * @param int $id 模块ID
     * @return bool
     * @throws ValidateException
     */
    public function toggleModuleStatus(int $id): bool
    {
        $module = PortalModule::find($id);
        if (!$module) {
            throw new ValidateException('模块不存在');
        }
        
        $result = PortalModule::toggleStatus($id);
        
        if ($result) {
            // 清除相关缓存
            $this->clearModuleCache($module->module_name);
        }
        
        return $result;
    }
    
    /**
     * 更新模块排序
     *
     * @param array $sortData 排序数据
     * @return bool
     * @throws ValidateException
     */
    public function updateModuleSort(array $sortData): bool
    {
        if (empty($sortData)) {
            throw new ValidateException('排序数据不能为空');
        }
        
        $result = PortalModule::updateSort($sortData);
        
        if ($result) {
            // 清除相关缓存
            $this->clearModuleCache();
        }
        
        return $result;
    }
    
    /**
     * 获取模块配置
     *
     * @param string $moduleName 模块名称
     * @param string $key 配置键名
     * @param mixed $default 默认值
     * @param bool $useCache 是否使用缓存
     * @return mixed
     */
    public function getModuleConfig(string $moduleName, string $key = '', $default = null, bool $useCache = true)
    {
        if ($useCache) {
            $cacheKey = self::CACHE_PREFIX . 'config:' . $moduleName . ':' . $key;
            $value = Cache::get($cacheKey);
            
            if ($value !== false) {
                return $value;
            }
        }
        
        $value = PortalModule::getModuleConfig($moduleName, $key, $default);
        
        if ($useCache) {
            Cache::set($cacheKey, $value, self::CACHE_EXPIRE);
        }
        
        return $value;
    }
    
    /**
     * 设置模块配置
     *
     * @param string $moduleName 模块名称
     * @param array $configData 配置数据
     * @return bool
     * @throws ValidateException
     */
    public function setModuleConfig(string $moduleName, array $configData): bool
    {
        $module = $this->getModuleByName($moduleName, false);
        if (!$module) {
            throw new ValidateException('模块不存在');
        }
        
        $result = PortalModule::setModuleConfig($moduleName, $configData);
        
        if ($result) {
            // 清除相关缓存
            $this->clearModuleCache($moduleName);
        }
        
        return $result;
    }
    
    /**
     * 验证模块数据
     *
     * @param array $data 模块数据
     * @param int $excludeId 排除的ID
     * @throws ValidateException
     */
    protected function validateModuleData(array $data, int $excludeId = 0): void
    {
        // 必填字段验证
        if (empty($data['module_name'])) {
            throw new ValidateException('模块名称不能为空');
        }
        
        if (empty($data['module_title'])) {
            throw new ValidateException('模块标题不能为空');
        }
        
        // 模块名格式验证
        if (!preg_match('/^[a-zA-Z][a-zA-Z0-9_]*$/', $data['module_name'])) {
            throw new ValidateException('模块名称格式不正确，只能包含字母、数字和下划线，且必须以字母开头');
        }
        
        // 列数验证
        if (isset($data['column_count']) && !PortalModule::validateColumnCount($data['column_count'])) {
            throw new ValidateException('列数只能是1或2');
        }
        
        // 配置数据验证
        if (isset($data['config_data'])) {
            if (is_string($data['config_data'])) {
                $decoded = json_decode($data['config_data'], true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    throw new ValidateException('配置数据JSON格式不正确');
                }
            } elseif (!is_array($data['config_data'])) {
                throw new ValidateException('配置数据必须是数组或JSON字符串');
            }
        }
    }
    
    /**
     * 获取下一个排序号
     *
     * @return int
     */
    protected function getNextSortOrder(): int
    {
        $maxSort = PortalModule::max('sort_order');
        return ($maxSort ?: 0) + 1;
    }
    
    /**
     * 清除模块缓存
     *
     * @param string $moduleName 模块名称
     */
    protected function clearModuleCache(string $moduleName = ''): void
    {
        // 清除启用模块列表缓存
        Cache::delete(self::CACHE_PREFIX . 'enabled');
        
        if (!empty($moduleName)) {
            // 清除特定模块缓存
            Cache::delete(self::CACHE_PREFIX . 'name:' . $moduleName);
            
            // 清除模块配置缓存
            $pattern = self::CACHE_PREFIX . 'config:' . $moduleName . ':*';
            // 注意：这里简化处理，实际项目中可能需要更精确的缓存清理策略
            Cache::clear();
        } else {
            // 清除所有模块相关缓存
            Cache::clear();
        }
    }
}
