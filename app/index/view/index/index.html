{include file="layout/head" /}
<body>
    {include file="layout/header" /}

    <!-- 主体内容 -->
    <main class="main-content">
        <div class="layui-container">
            {if isset($error)}
            <div class="error-message">
                <strong>系统提示：</strong>{$error}
            </div>
            {/if}

            <!-- 动态渲染模块 -->
            {volist name="modules" id="module"}
            <div class="module-container module-{$module.column_count == 2 ? 'two-column' : 'one-column'}">
                {switch name="module.module_name"}
                    {case value="news_banner"}
                        {include file="modules/banner" /}
                    {/case}
                    {case value="hot_news"}
                        {include file="modules/hot_news" /}
                    {/case}
                    {case value="category_nav"}
                        {include file="modules/category_nav" /}
                    {/case}
                    {case value="latest_articles"}
                        {include file="modules/latest_articles" /}
                    {/case}
                    {case value="sidebar_ads"}
                        {include file="modules/sidebar_ads" /}
                    {/case}
                    {default /}
                        <div class="module-header">{$module.module_title}</div>
                        <div class="module-body">
                            <p>模块 "{$module.module_name}" 暂未实现</p>
                        </div>
                {/switch}
            </div>
            {/volist}

            {if empty($modules)}
            <div class="module-container">
                <div class="module-header">欢迎访问</div>
                <div class="module-body">
                    <p>门户配置系统正在加载中，请稍后...</p>
                    <p>如果您是管理员，请前往 <a href="/admin/portal_module.html">模块配置</a> 启用相关模块。</p>
                </div>
            </div>
            {/if}
        </div>
    </main>

    {include file="layout/footer" /}

    {include file="layout/scripts" /}
</body>
</html>
