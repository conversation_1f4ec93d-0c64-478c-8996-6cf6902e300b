{include file="layout/head" /}
<body>
    {include file="layout/header" /}

    <!-- 主体内容 -->
    <main class="main-content">
        <div class="layui-container">
            <!-- 分类页面特有样式 -->
            <style>
                .category-header {
                    background: #fff;
                    padding: 20px;
                    margin-bottom: 20px;
                    border-radius: 8px;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                }
                .category-title {
                    font-size: 24px;
                    color: #333;
                    margin-bottom: 10px;
                }
                .category-description {
                    color: #666;
                    font-size: 14px;
                }

                .article-list {
                    background: #fff;
                    border-radius: 8px;
                    overflow: hidden;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                }
                .article-item {
                    display: flex;
                    padding: 20px;
                    border-bottom: 1px solid #f0f0f0;
                    transition: all 0.3s;
                }
                .article-item:hover {
                    background: #fafafa;
                }
                .article-item:last-child {
                    border-bottom: none;
                }
                .article-image {
                    width: 150px;
                    height: 100px;
                    background-size: cover;
                    background-position: center;
                    border-radius: 6px;
                    margin-right: 20px;
                    flex-shrink: 0;
                }
                .article-content {
                    flex: 1;
                }
                .article-title {
                    font-size: 18px;
                    font-weight: bold;
                    color: #333;
                    margin-bottom: 10px;
                    line-height: 1.4;
                    text-decoration: none;
                }
                .article-title:hover {
                    color: #1890ff;
                }
                .article-meta {
                    font-size: 12px;
                    color: #999;
                    margin-bottom: 10px;
                }
                .article-summary {
                    font-size: 14px;
                    color: #666;
                    line-height: 1.6;
                    display: -webkit-box;
                    -webkit-line-clamp: 3;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                }

                .pagination {
                    text-align: center;
                    margin-top: 30px;
                }

                @media (max-width: 768px) {
                    .article-item {
                        flex-direction: column;
                    }
                    .article-image {
                        width: 100%;
                        height: 200px;
                        margin-right: 0;
                        margin-bottom: 15px;
                    }
                }
            </style>
            <!-- 分类头部 -->
            <div class="category-header">
                <h1 class="category-title">{$category.name}</h1>
                {if !empty($category.description)}
                <p class="category-description">{$category.description}</p>
                {/if}
            </div>
            
            <!-- 文章列表 -->
            <div class="article-list">
                {if !empty($articles)}
                {volist name="articles" id="article"}
                <div class="article-item">
                    {if !empty($article.cover_image)}
                    <div class="article-image" style="background-image: url('{$article.cover_image}');"></div>
                    {else}
                    <div class="article-image" style="background: #f0f0f0; display: flex; align-items: center; justify-content: center;">
                        <i class="layui-icon layui-icon-picture" style="font-size: 32px; color: #ccc;"></i>
                    </div>
                    {/if}
                    <div class="article-content">
                        <a href="/article/{$article.id}" class="article-title">{$article.title}</a>
                        <div class="article-meta">
                            <span><i class="layui-icon layui-icon-time"></i> {$article.publish_time|date='Y-m-d H:i'}</span>
                            <span style="margin-left: 15px;"><i class="layui-icon layui-icon-username"></i> {$article.author ?? '佚名'}</span>
                            <span style="margin-left: 15px;"><i class="layui-icon layui-icon-read"></i> {$article.view_count ?? 0}</span>
                        </div>
                        {if !empty($article.summary)}
                        <div class="article-summary">{$article.summary}</div>
                        {/if}
                    </div>
                </div>
                {/volist}
                {else}
                <div style="text-align: center; padding: 50px; color: #999;">
                    <i class="layui-icon layui-icon-file" style="font-size: 48px; margin-bottom: 15px;"></i>
                    <p>该分类下暂无文章</p>
                </div>
                {/if}
            </div>
            
            <!-- 分页 -->
            {if !empty($articles)}
            <div class="pagination">
                <!-- 暂时移除分页功能 -->
            </div>
            {/if}
        </div>
    </main>

    {include file="layout/footer" /}

    {include file="layout/scripts" /}
</body>
</html>
