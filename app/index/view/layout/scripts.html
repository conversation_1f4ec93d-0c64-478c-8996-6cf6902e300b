<script src="/static/layui/layui.js"></script>
<script>
    layui.use(['carousel', 'element'], function () {
        var carousel = layui.carousel;
        var element = layui.element;

        // 初始化轮播
        if (document.getElementById('newsBanner')) {
            carousel.render({
                elem: '#newsBanner',
                width: '100%',
                height: '400px',
                interval: 5000,
                anim: 'fade'
            });
        }
    });

    // 更新时间
    function updateTime() {
        const now = new Date();
        const timeStr = now.getFullYear() + '年' +
            (now.getMonth() + 1).toString().padStart(2, '0') + '月' +
            now.getDate().toString().padStart(2, '0') + '日 ' +
            now.getHours().toString().padStart(2, '0') + ':' +
            now.getMinutes().toString().padStart(2, '0');
        document.getElementById('currentTime').textContent = timeStr;
    }

    // 搜索功能
    function searchNews() {
        const keyword = document.getElementById('searchInput').value.trim();
        if (keyword) {
            window.location.href = '/search?q=' + encodeURIComponent(keyword);
        }
    }

    // 回车搜索
    document.getElementById('searchInput').addEventListener('keypress', function (e) {
        if (e.key === 'Enter') {
            searchNews();
        }
    });

    // 初始化
    updateTime();
    setInterval(updateTime, 60000); // 每分钟更新一次时间
</script>