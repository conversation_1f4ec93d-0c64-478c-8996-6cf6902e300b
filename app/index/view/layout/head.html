<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{$title}</title>
    <meta name="keywords" content="{$seoConfigs.site_keywords ?? '新闻,资讯,门户'}">
    <meta name="description" content="{$seoConfigs.site_description ?? '专业的新闻资讯门户网站'}">
    <link rel="stylesheet" href="/static/layui/css/layui.css">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; background-color: #f5f5f5; }

        /* 头部样式 */
        .header {
            background: #fff;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }
        .header-top {
            background: #1890ff;
            color: #fff;
            padding: 8px 0;
            font-size: 12px;
        }
        .header-main {
            padding: 15px 0;
        }
        .logo {
            font-size: 28px;
            font-weight: bold;
            color: #1890ff;
            text-decoration: none;
        }
        .search-box {
            position: relative;
        }
        .search-input {
            width: 300px;
            height: 40px;
            border: 2px solid #1890ff;
            border-radius: 20px;
            padding: 0 50px 0 20px;
            outline: none;
        }
        .search-btn {
            position: absolute;
            right: 5px;
            top: 5px;
            width: 30px;
            height: 30px;
            background: #1890ff;
            border: none;
            border-radius: 15px;
            color: #fff;
            cursor: pointer;
        }

        /* 导航样式 */
        .nav-menu {
            background: #fff;
            border-top: 1px solid #e6e6e6;
            padding: 0;
        }
        .nav-item {
            display: inline-block;
            padding: 15px 25px;
            color: #333;
            text-decoration: none;
            transition: all 0.3s;
            border-bottom: 3px solid transparent;
        }
        .nav-item:hover, .nav-item.active {
            color: #1890ff;
            border-bottom-color: #1890ff;
        }

        /* 主体内容 */
        .main-content {
            padding: 20px 0;
        }
        .module-container {
            margin-bottom: 30px;
            background: #fff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .module-header {
            background: #fafafa;
            padding: 15px 20px;
            border-bottom: 1px solid #e6e6e6;
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
        .module-body {
            padding: 20px;
        }

        /* 轮播样式 */
        .banner-item {
            position: relative;
            height: 400px;
            background-size: cover;
            background-position: center;
            display: flex;
            align-items: flex-end;
        }
        .banner-content {
            background: linear-gradient(transparent, rgba(0,0,0,0.7));
            color: #fff;
            padding: 30px;
            width: 100%;
        }
        .banner-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
            line-height: 1.4;
        }
        .banner-summary {
            font-size: 14px;
            opacity: 0.9;
            line-height: 1.6;
        }

        /* 新闻列表样式 */
        .news-list {
            list-style: none;
        }
        .news-item {
            display: flex;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
            transition: all 0.3s;
        }
        .news-item:hover {
            background: #fafafa;
            margin: 0 -20px;
            padding: 15px 20px;
        }
        .news-item:last-child {
            border-bottom: none;
        }
        .news-image {
            width: 120px;
            height: 80px;
            background-size: cover;
            background-position: center;
            border-radius: 6px;
            margin-right: 15px;
            flex-shrink: 0;
        }
        .news-content {
            flex: 1;
        }
        .news-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
            line-height: 1.4;
            text-decoration: none;
        }
        .news-title:hover {
            color: #1890ff;
        }
        .news-meta {
            font-size: 12px;
            color: #999;
            margin-bottom: 8px;
        }
        .news-summary {
            font-size: 14px;
            color: #666;
            line-height: 1.5;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        /* 分类导航样式 */
        .category-nav {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }
        .category-item {
            padding: 10px 20px;
            background: #f8f9fa;
            border-radius: 20px;
            color: #333;
            text-decoration: none;
            transition: all 0.3s;
            border: 1px solid #e9ecef;
        }
        .category-item:hover {
            background: #1890ff;
            color: #fff;
            border-color: #1890ff;
        }

        /* 广告样式 */
        .ad-container {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border: 2px dashed #ddd;
            border-radius: 8px;
            color: #999;
        }

        /* 外链模块样式 */
        .external-links {
            background: #f8f9fa;
            padding: 40px 0;
            margin-top: 50px;
            border-top: 1px solid #e6e6e6;
        }
        .links-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .links-header h3 {
            font-size: 24px;
            color: #333;
            margin: 0;
            font-weight: bold;
        }
        .links-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }
        .links-category {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .category-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #1890ff;
        }
        .links-list {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
        }
        .external-link {
            display: block;
            padding: 8px 12px;
            background: #f8f9fa;
            color: #333;
            text-decoration: none;
            border-radius: 4px;
            transition: all 0.3s;
            font-size: 14px;
            border: 1px solid #e9ecef;
        }
        .external-link:hover {
            background: #1890ff;
            color: #fff;
            border-color: #1890ff;
            transform: translateY(-1px);
        }

        /* 底部样式 */
        .footer {
            background: #333;
            color: #fff;
            padding: 20px 0;
        }
        .footer-bottom {
            text-align: center;
            color: #999;
            font-size: 12px;
        }
        .footer-bottom a {
            color: #999;
            text-decoration: none;
            margin: 0 5px;
        }
        .footer-bottom a:hover {
            color: #1890ff;
        }

        /* 响应式布局 */
        .module-one-column {
            width: 100%;
        }
        .module-two-column {
            width: 48%;
            display: inline-block;
            vertical-align: top;
            margin-right: 2%;
        }
        .module-two-column:nth-child(2n) {
            margin-right: 0;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .module-two-column {
                width: 100%;
                margin-right: 0;
                margin-bottom: 20px;
            }
            .search-input {
                width: 200px;
            }
            .banner-title {
                font-size: 18px;
            }
            .news-image {
                width: 80px;
                height: 60px;
            }
            .links-content {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            .links-list {
                grid-template-columns: 1fr;
            }
            .external-links {
                padding: 20px 0;
            }
        }

        /* 错误提示样式 */
        .error-message {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
        }
    </style>
</head>
