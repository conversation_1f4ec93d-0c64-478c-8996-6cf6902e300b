<?php
declare (strict_types = 1);

namespace app\index\controller;

use app\BaseController;
use app\common\service\PortalConfigService;
use app\common\service\PortalModuleService;
use app\common\service\ArticleCategoryService;
use app\common\repository\ArticleRepository;

use think\facade\View;

class Index extends BaseController
{
    protected ArticleCategoryService $categoryService;
    protected ArticleRepository $articleRepository;

    public function __construct(\think\App $app)
    {
        parent::__construct($app);
        $this->categoryService = new ArticleCategoryService();
        $this->articleRepository = new ArticleRepository();
    }

    /**
     * 测试页面
     */
    public function test()
    {
        return View::fetch('index/test', [
            'title' => '测试页面',
            'data' => [
                'timestamp' => time(),
                'random' => mt_rand(1000, 9999),
                'user_agent' => $this->request->header('user-agent')
            ]
        ]);
    }

    /**
     * 验证码测试
     */
    public function captcha()
    {
        return View::fetch('index/captcha', [
            'title' => '验证码测试'
        ]);
    }

    /**
     * 首页 - 门户页面
     */
    public function index()
    {
        try {
            $configService = new PortalConfigService();
            $moduleService = new PortalModuleService();

            // 获取基础配置，确保返回数组
            $basicConfigs = $configService->getConfigByGroup('basic') ?: [];
            $seoConfigs = $configService->getConfigByGroup('seo') ?: [];

            // 获取启用的模块，确保返回数组
            $modules = $moduleService->getEnabledModules() ?: [];

            // 获取新闻数据
            $newsData = $this->getNewsData();

            // 获取分类数据（使用缓存）
            $categories = $this->categoryService->getCachedTopLevel(true);
            // 转换为数组格式并限制数量
            $categories = array_slice(array_map(function($category) {
                return $category->toArray();
            }, $categories), 0, 10);

            return View::fetch('index/index', [
                'basicConfigs' => $basicConfigs,
                'seoConfigs' => $seoConfigs,
                'modules' => $modules,
                'newsData' => $newsData,
                'categories' => $categories,
                'title' => $basicConfigs['site_name'] ?? '新闻门户网站'
            ]);

        } catch (\Exception $e) {
            // 如果出错，显示默认页面
            return View::fetch('index/index', [
                'basicConfigs' => ['site_name' => '新闻门户网站'],
                'seoConfigs' => [],
                'modules' => [],
                'newsData' => ['banner' => [], 'hot' => [], 'latest' => []],
                'categories' => [],
                'title' => '新闻门户网站',
                'error' => '系统初始化中，请稍后访问或联系管理员'
            ]);
        }
    }

    /**
     * 获取新闻数据
     */
    private function getNewsData(): array
    {
        try {
            // 获取轮播新闻（最新5条有封面图的新闻）
            $bannerArticles = $this->articleRepository->getBannerArticles(5);
            $bannerNews = array_map(function($article) {
                $data = $article->toArray();
                // 这里可以添加分类名称，如果需要的话
                return $data;
            }, $bannerArticles);

            // 获取热门新闻（按浏览量排序）
            $hotArticles = $this->articleRepository->getHotArticles(10);
            $hotNews = array_map(function($article) {
                $data = $article->toArray();
                return $data;
            }, $hotArticles);

            // 获取最新文章
            $latestArticles = $this->articleRepository->getLatestArticles(12);
            $latestNews = array_map(function($article) {
                $data = $article->toArray();
                return $data;
            }, $latestArticles);

            return [
                'banner' => $bannerNews,
                'hot' => $hotNews,
                'latest' => $latestNews
            ];
        } catch (\Exception $e) {
            // 如果数据库查询失败，返回空数组
            return [
                'banner' => [],
                'hot' => [],
                'latest' => []
            ];
        }
    }

    /**
     * 分类页面
     */
    public function category()
    {
        $categoryId = (int)$this->request->param('id', 0);

        try {
            // 获取分类信息
            $categoryBean = $this->categoryService->getById($categoryId);
            if (!$categoryBean || !$categoryBean->isShow) {
                throw new \Exception('分类不存在');
            }
            $category = $categoryBean->toArray();

            // 获取该分类下的文章
            $articleBeans = $this->articleRepository->findByCategoryId($categoryId, 1, 20);
            $articles = array_map(function($article) {
                return $article->toArray();
            }, $articleBeans['list']);

            // 获取所有分类用于导航（使用缓存）
            $categoryBeans = $this->categoryService->getCachedTopLevel(true);
            $categories = array_slice(array_map(function($cat) {
                return $cat->toArray();
            }, $categoryBeans), 0, 10);

            return View::fetch('index/category', [
                'category' => $category,
                'articles' => $articles,
                'categories' => $categories,
                'title' => $category['name'] . ' - 新闻列表'
            ]);

        } catch (\Exception $e) {
            die($e->getMessage());
            // return redirect('/');
        }
    }

    /**
     * 文章详情页面
     */
    public function article()
    {
        $articleId = (int)$this->request->param('id', 0);

        try {
            // 获取文章信息
            $articleBean = $this->articleRepository->findById($articleId);
            if (!$articleBean || !$articleBean->isPublished()) {
                return redirect('/');
            }

            // 增加浏览量
            $this->articleRepository->incrementViewCount($articleId);

            // 转换为数组
            $article = $articleBean->toArray();
            $article['view_count'] = ($article['view_count'] ?? 0) + 1;

            // 获取文章的自定义字段
            $customFields = $this->getArticleCustomFields($articleId);

            // 获取相关文章（同分类的其他文章）
            $relatedArticleBeans = $this->articleRepository->getRelatedArticles(
                $articleBean->categoryId,
                $articleId,
                6
            );
            $relatedArticles = array_map(function($relatedArticle) {
                return $relatedArticle->toArray();
            }, $relatedArticleBeans);

            // 获取所有分类用于导航（使用缓存）
            $categoryBeans = $this->categoryService->getCachedTopLevel(true);
            $categories = array_slice(array_map(function($cat) {
                return $cat->toArray();
            }, $categoryBeans), 0, 10);

            return View::fetch('index/article', [
                'article' => $article,
                'customFields' => $customFields,
                'relatedArticles' => $relatedArticles,
                'categories' => $categories,
                'title' => $article['title'] . ' - ' . $article['category_name']
            ]);

        } catch (\Exception $e) {
            return redirect('/');
        }
    }

    /**
     * 获取文章的自定义字段
     */
    private function getArticleCustomFields(int $articleId): array
    {
        try {
            // 暂时返回空数组，在实际项目中可以实现完整的自定义字段查询
            // 这需要更复杂的Repository方法或者使用Service层来处理
            return [];
        } catch (\Exception $e) {
            return [];
        }
    }
}
