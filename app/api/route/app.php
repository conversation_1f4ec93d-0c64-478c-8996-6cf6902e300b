<?php
use think\facade\Route;

// API根路径
Route::get('/', 'app\api\controller\TestController@index');

// 测试路由
Route::get('test', 'app\api\controller\TestController@index');
Route::get('health', 'app\api\controller\TestController@health');

// 认证路由
Route::group('auth', function () {
    Route::get('check', 'app\api\controller\AuthController@check');
    Route::post('login', 'app\api\controller\AuthController@login');
    Route::get('login', 'app\api\controller\AuthController@login'); // 同时支持GET请求用于测试
});

// 分类管理路由 (无注解版本)
Route::group('categories', function () {
    Route::get('tree', 'app\api\controller\SimpleCategoryController@tree');
    Route::get('/', 'app\api\controller\SimpleCategoryController@index');
    Route::get('<id>', 'app\api\controller\SimpleCategoryController@read');
});

// 文章管理路由 (无注解版本)
Route::group('articles', function () {
    Route::get('hot', 'app\api\controller\PublicArticleController@hot');
    Route::get('latest', 'app\api\controller\PublicArticleController@latest');
    Route::get('banner', 'app\api\controller\PublicArticleController@banner');
    Route::get('search', 'app\api\controller\PublicArticleController@search');
    Route::get('/', 'app\api\controller\PublicArticleController@index');
    Route::get('<id>', 'app\api\controller\PublicArticleController@read');
});

// 公开门户API路由
Route::group('public-portal', function () {
    Route::get('modules/enabled', 'app\api\controller\PublicPortalController@enabledModules');
    Route::get('configs/<group>', 'app\api\controller\PublicPortalController@configsByGroup');
    Route::get('home-data', 'app\api\controller\PublicPortalController@homeData');
});
