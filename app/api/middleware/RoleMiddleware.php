<?php
declare(strict_types=1);

namespace app\api\middleware;

use app\common\service\AdminAuthService;
use app\api\annotation\RequireRole;
use think\Request;
use think\Response;
use Closure;
use ReflectionClass;
use ReflectionMethod;

/**
 * API角色权限中间件
 */
class RoleMiddleware
{
    /**
     * 处理请求
     */
    public function handle(Request $request, Closure $next): Response
    {
        // 获取当前路由信息
        $pathInfo = trim($request->pathinfo(), '/');

        // 排除不需要权限验证的路由
        $excludeRoutes = [
            'api/auth/login',
            'api/auth/logout',
            'api/auth/info',
            'api/auth/check',
            'api/test/bean',
            'api/test/service',
            'api/',
            'api/test'
        ];

        // 检查是否匹配排除路由
        foreach ($excludeRoutes as $excludeRoute) {
            if ($pathInfo === $excludeRoute || str_ends_with($pathInfo, $excludeRoute)) {
                return $next($request);
            }
        }

        // 获取控制器和方法
        $controller = $request->controller();
        $action = $request->action();

        // 如果获取不到，尝试从路由信息中解析
        if (empty($controller) || empty($action)) {
            $pathInfo = trim($request->pathinfo(), '/');
            $pathParts = explode('/', $pathInfo);

            // 调试信息
            if (app()->isDebug()) {
                trace("API RoleMiddleware - PathInfo: {$pathInfo}, PathParts: " . json_encode($pathParts));
            }

            if (count($pathParts) >= 2) {
                // API路由格式：api/users -> UserController
                $controller = $pathParts[1] ?? '';
                $action = $pathParts[2] ?? 'index';

                // 转换控制器名称
                $controllerMap = [
                    'auth' => 'AuthController',
                    'users' => 'UserController',
                    'categories' => 'ArticleCategoryController',
                    'articles' => 'ArticleController',
                    'article-custom-fields' => 'ArticleCustomFieldController',
                    'public-categories' => 'PublicCategoryController'
                ];
                $controller = $controllerMap[$controller] ?? ucfirst($controller) . 'Controller';
            }
        }

        $authService = new AdminAuthService();

        // 检查权限注解
        $roleAnnotation = $this->getRoleAnnotation($controller, $action);

        // 调试信息
        if (app()->isDebug()) {
            $debugInfo = $roleAnnotation ? [
                'hasAnnotation' => true,
                'isSuper' => $roleAnnotation->isSuper(),
                'roles' => $roleAnnotation->getRoles()
            ] : ['hasAnnotation' => false];
            trace("API RoleMiddleware - Controller: {$controller}, Action: {$action}, Permission: " . json_encode($debugInfo));
        }

        // 如果有权限注解，则需要检查权限
        if ($roleAnnotation) {
            // 检查是否需要超级管理员权限
            if ($roleAnnotation->isSuper()) {
                if (!$authService->hasRole('super_admin')) {
                    return json([
                        'code' => 403,
                        'message' => '需要超级管理员权限',
                        'data' => null
                    ], 403);
                }
            } else {
                // 兼容旧版本的角色检查
                $requiredRoles = $roleAnnotation->getRoles();
                if (!empty($requiredRoles)) {
                    if (!$authService->hasAnyRole($requiredRoles)) {
                        return json([
                            'code' => 403,
                            'message' => '权限不足，需要角色：' . implode('、', $this->translateRoles($requiredRoles)),
                            'data' => null
                        ], 403);
                    }
                } else {
                    // 没有指定具体角色，只要是登录的管理员即可
                    // 这里不需要额外检查，因为AuthMiddleware已经验证了登录状态
                }
            }
        }
        // 如果没有权限注解，则为公开接口，不需要权限检查

        return $next($request);
    }
    
    /**
     * 获取权限注解
     */
    private function getRoleAnnotation(string $controller, string $action): ?RequireRole
    {
        try {
            // 尝试不同的命名空间
            $possibleClasses = [
                "app\\api\\controller\\{$controller}",
                "app\\api\\controller\\admin\\{$controller}"
            ];

            $controllerClass = null;
            foreach ($possibleClasses as $class) {
                if (class_exists($class)) {
                    $controllerClass = $class;
                    break;
                }
            }

            if (!$controllerClass) {
                if (app()->isDebug()) {
                    trace("API RoleMiddleware - Controller class not found: " . implode(', ', $possibleClasses));
                }
                return null;
            }

            $reflectionClass = new ReflectionClass($controllerClass);

            // 检查方法注解
            if ($reflectionClass->hasMethod($action)) {
                $reflectionMethod = new ReflectionMethod($controllerClass, $action);
                $attributes = $reflectionMethod->getAttributes(RequireRole::class);

                if (app()->isDebug()) {
                    trace("API RoleMiddleware - Found " . count($attributes) . " RequireRole attributes for {$controllerClass}::{$action}");
                }

                if (!empty($attributes)) {
                    return $attributes[0]->newInstance();
                }
            } else {
                if (app()->isDebug()) {
                    trace("API RoleMiddleware - Method {$action} not found in {$controllerClass}");
                }
            }

            // 检查类注解
            $classAttributes = $reflectionClass->getAttributes(RequireRole::class);
            if (!empty($classAttributes)) {
                return $classAttributes[0]->newInstance();
            }
            
        } catch (\Exception $e) {
            // 忽略反射异常
            if (app()->isDebug()) {
                trace("API RoleMiddleware - Exception: " . $e->getMessage());
            }
        }

        return null;
    }
    
    /**
     * 翻译角色名称
     */
    private function translateRoles(array $roles): array
    {
        $roleMap = [
            'super_admin' => '超级管理员',
            'content_admin' => '内容管理员',
        ];
        
        return array_map(function($role) use ($roleMap) {
            return $roleMap[$role] ?? $role;
        }, $roles);
    }
}
