<?php
declare(strict_types=1);

namespace app\api\middleware;

use app\api\service\AdminAuthService;
use think\Request;
use think\Response;
use Closure;

/**
 * API管理员认证中间件
 */
class AuthMiddleware
{
    /**
     * 处理请求
     */
    public function handle(Request $request, Closure $next): Response
    {
        // 获取当前路由信息
        $pathInfo = trim($request->pathinfo(), '/');

        // 排除不需要认证的路由
        $excludeRoutes = [
            'api/admin/auth/login',
            'api/admin/auth/check',
            'api/test/bean',
            'api/test/service',
            'api/',
            'api/test'
        ];

        // 检查是否匹配排除路由
        foreach ($excludeRoutes as $excludeRoute) {
            if ($pathInfo === $excludeRoute || str_ends_with($pathInfo, $excludeRoute)) {
                return $next($request);
            }
        }

        $authService = new AdminAuthService();

        // 检查是否已登录
        if (!$authService->isLogin()) {
            return json([
                'code' => 401,
                'message' => '请先登录',
                'data' => null
            ], 401);
        }

        // 刷新用户信息（检查用户状态）
        if (!$authService->refreshUser()) {
            return json([
                'code' => 401,
                'message' => '登录状态已失效，请重新登录',
                'data' => null
            ], 401);
        }

        return $next($request);
    }
}
