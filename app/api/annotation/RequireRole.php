<?php
declare(strict_types=1);

namespace app\api\annotation;

use Attribute;

/**
 * 角色权限注解
 *
 * 使用示例：
 * #[RequireRole()] - 需要登录（任何管理员）
 * #[RequireRole(isSuper: true)] - 需要超级管理员
 * #[RequireRole(['super_admin', 'content_admin'])] - 兼容旧版本
 */
#[Attribute(Attribute::TARGET_METHOD | Attribute::TARGET_CLASS)]
class RequireRole
{
    /**
     * 需要的角色（兼容旧版本）
     * @var array|string|null
     */
    public array|string|null $roles;

    /**
     * 是否需要超级管理员权限
     * @var bool
     */
    public bool $isSuper;

    /**
     * 错误消息
     * @var string
     */
    public string $message;

    /**
     * 构造函数
     *
     * @param array|string|null $roles 需要的角色（兼容旧版本）
     * @param bool $isSuper 是否需要超级管理员权限
     * @param string $message 权限不足时的错误消息
     */
    public function __construct(
        array|string|null $roles = null,
        bool $isSuper = false,
        string $message = '权限不足'
    ) {
        $this->roles = $roles;
        $this->isSuper = $isSuper;
        $this->message = $message;
    }
    
    /**
     * 获取需要的角色列表（兼容旧版本）
     */
    public function getRoles(): array
    {
        if ($this->roles === null) {
            return [];
        }
        return is_array($this->roles) ? $this->roles : [$this->roles];
    }

    /**
     * 是否需要超级管理员权限
     */
    public function isSuper(): bool
    {
        return $this->isSuper;
    }

    /**
     * 是否需要登录（有权限注解就需要登录）
     */
    public function requiresAuth(): bool
    {
        return true;
    }

    /**
     * 获取错误消息
     */
    public function getMessage(): string
    {
        return $this->message;
    }
}
