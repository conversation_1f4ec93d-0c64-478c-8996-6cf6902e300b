<?php
declare(strict_types=1);

namespace app\api\controller;

use think\Response;

/**
 * 认证控制器
 */
class AuthController
{
    /**
     * 检查认证状态
     */
    public function check(): Response
    {
        return json([
            "status" => "success",
            "message" => "认证检查接口正常",
            "data" => [
                "authenticated" => false,
                "timestamp" => date("Y-m-d H:i:s"),
                "app" => "api",
                "controller" => "AuthController",
                "method" => "check"
            ]
        ]);
    }
    
    /**
     * 登录接口
     */
    public function login(): Response
    {
        return json([
            "status" => "success",
            "message" => "登录接口",
            "data" => [
                "login_url" => "/api/auth/login",
                "method" => "POST",
                "required_fields" => ["username", "password"],
                "timestamp" => date("Y-m-d H:i:s")
            ]
        ]);
    }
}
