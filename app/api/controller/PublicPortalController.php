<?php
declare(strict_types=1);

namespace app\api\controller;

use think\Response;
use app\common\service\PortalModuleService;
use app\common\service\PortalConfigService;

/**
 * 公开门户API控制器
 */
class PublicPortalController
{
    protected PortalModuleService $moduleService;
    protected PortalConfigService $configService;
    
    public function __construct()
    {
        $this->moduleService = new PortalModuleService();
        $this->configService = new PortalConfigService();
    }
    
    /**
     * 获取启用的门户模块
     */
    public function enabledModules(): Response
    {
        try {
            $modules = $this->moduleService->getEnabledModules();
            
            return json([
                'status' => 'success',
                'message' => '获取启用模块成功',
                'data' => [
                    'modules' => $modules,
                    'total' => count($modules),
                    'timestamp' => date('Y-m-d H:i:s')
                ]
            ]);
        } catch (\Exception $e) {
            return json([
                'status' => 'error',
                'message' => '获取启用模块失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 根据分组获取配置
     */
    public function configsByGroup($group): Response
    {
        try {
            $configs = $this->configService->getConfigByGroup($group);

            return json([
                'status' => 'success',
                'message' => '获取配置成功',
                'data' => [
                    'group' => $group,
                    'configs' => $configs,
                    'total' => count($configs),
                    'timestamp' => date('Y-m-d H:i:s')
                ]
            ]);
        } catch (\Exception $e) {
            return json([
                'status' => 'error',
                'message' => '获取配置失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取门户首页数据
     */
    public function homeData(): Response
    {
        try {
            // 获取启用的模块
            $modules = $this->moduleService->getEnabledModules();
            
            // 获取基础配置
            $siteConfigs = $this->configService->getConfigByGroup('site');
            
            return json([
                'status' => 'success',
                'message' => '获取首页数据成功',
                'data' => [
                    'modules' => $modules,
                    'site_configs' => $siteConfigs,
                    'timestamp' => date('Y-m-d H:i:s')
                ]
            ]);
        } catch (\Exception $e) {
            return json([
                'status' => 'error',
                'message' => '获取首页数据失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
