<?php
declare(strict_types=1);

namespace app\api\controller;

use app\api\annotation\RequireRole;
use app\common\service\ArticleCustomFieldService;
use think\Request;
use think\Response;
use think\exception\ValidateException;

/**
 * 文章自定义字段管理控制器
 */
class ArticleCustomFieldController
{
    private ArticleCustomFieldService $fieldService;

    public function __construct()
    {
        $this->fieldService = new ArticleCustomFieldService();
    }

    /**
     * 获取字段列表
     *
     * @param Request $request
     * @return Response
     */
    #[RequireRole(message: '查看字段列表需要管理员权限')]
    public function index(Request $request): Response
    {
        try {
            $page = (int)$request->param('page', 1);
            $limit = (int)$request->param('limit', 15);
            
            // 构建查询条件
            $where = [];
            if ($request->has('name')) {
                $where['name'] = $request->param('name');
            }
            if ($request->has('field_key')) {
                $where['field_key'] = $request->param('field_key');
            }
            if ($request->has('field_type')) {
                $where['field_type'] = $request->param('field_type');
            }
            if ($request->has('is_required')) {
                $where['is_required'] = $request->param('is_required');
            }
            if ($request->has('is_active')) {
                $where['is_active'] = $request->param('is_active');
            }
            
            $result = $this->fieldService->getList($where, $page, $limit);
            
            // 转换Bean为数组
            $list = [];
            foreach ($result['list'] as $field) {
                $list[] = $field->toArray();
            }
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => [
                    'list' => $list,
                    'total' => $result['total'],
                    'page' => $result['page'],
                    'limit' => $result['limit'],
                    'pages' => $result['pages']
                ]
            ]);
            
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取字段列表失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取所有启用的字段
     *
     * @param Request $request
     * @return Response
     */
    #[RequireRole(message: '查看字段列表需要管理员权限')]
    public function active(Request $request): Response
    {
        try {
            $fields = $this->fieldService->getActiveFields();
            
            // 转换Bean为数组
            $list = [];
            foreach ($fields as $field) {
                $list[] = $field->toArray();
            }
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $list
            ]);
            
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取启用字段失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取字段详情
     *
     * @param Request $request
     * @return Response
     */
    #[RequireRole(message: '查看字段详情需要管理员权限')]
    public function read(Request $request): Response
    {
        try {
            $id = (int)$request->param('id');
            if (!$id) {
                throw new ValidateException('字段ID不能为空');
            }
            
            $field = $this->fieldService->getById($id);
            if (!$field) {
                return json([
                    'code' => 404,
                    'message' => '字段不存在',
                    'data' => null
                ], 404);
            }
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $field->toArray()
            ]);
            
        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取字段详情失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 创建字段
     *
     * @param Request $request
     * @return Response
     */
    #[RequireRole(roles: ['super_admin'], message: '创建字段需要超级管理员权限')]
    public function create(Request $request): Response
    {
        try {
            $data = $request->only([
                'name', 'field_key', 'field_type', 'is_required', 'default_value',
                'options', 'sort_order', 'is_active', 'description', 'validation_rules'
            ]);
            
            $field = $this->fieldService->create($data);
            
            return json([
                'code' => 200,
                'message' => '创建成功',
                'data' => $field->toArray()
            ]);
            
        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '创建字段失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 更新字段
     *
     * @param Request $request
     * @return Response
     */
    #[RequireRole(roles: ['super_admin'], message: '更新字段需要超级管理员权限')]
    public function update(Request $request): Response
    {
        try {
            $id = (int)$request->param('id');
            if (!$id) {
                throw new ValidateException('字段ID不能为空');
            }
            
            $data = $request->only([
                'name', 'field_key', 'field_type', 'is_required', 'default_value',
                'options', 'sort_order', 'is_active', 'description', 'validation_rules'
            ]);
            
            $field = $this->fieldService->update($id, $data);
            
            return json([
                'code' => 200,
                'message' => '更新成功',
                'data' => $field->toArray()
            ]);
            
        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '更新字段失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 删除字段
     *
     * @param Request $request
     * @return Response
     */
    #[RequireRole(roles: ['super_admin'], message: '删除字段需要超级管理员权限')]
    public function delete(Request $request): Response
    {
        try {
            $id = (int)$request->param('id');
            if (!$id) {
                throw new ValidateException('字段ID不能为空');
            }
            
            $result = $this->fieldService->delete($id);
            
            return json([
                'code' => 200,
                'message' => '删除成功',
                'data' => ['result' => $result]
            ]);
            
        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '删除字段失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 切换启用状态
     *
     * @param Request $request
     * @return Response
     */
    #[RequireRole(roles: ['super_admin'], message: '切换字段状态需要超级管理员权限')]
    public function toggleActive(Request $request): Response
    {
        try {
            $id = (int)$request->param('id');
            if (!$id) {
                throw new ValidateException('字段ID不能为空');
            }
            
            $result = $this->fieldService->toggleActive($id);
            
            return json([
                'code' => 200,
                'message' => '操作成功',
                'data' => ['result' => $result]
            ]);
            
        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '切换字段状态失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 批量更新排序
     *
     * @param Request $request
     * @return Response
     */
    #[RequireRole(roles: ['super_admin'], message: '更新排序需要超级管理员权限')]
    public function updateSort(Request $request): Response
    {
        try {
            $sortData = $request->param('sort_data', []);
            if (empty($sortData) || !is_array($sortData)) {
                throw new ValidateException('排序数据不能为空');
            }
            
            $result = $this->fieldService->updateSort($sortData);
            
            return json([
                'code' => 200,
                'message' => '更新成功',
                'data' => ['result' => $result]
            ]);
            
        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '更新排序失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取字段统计信息
     *
     * @param Request $request
     * @return Response
     */
    #[RequireRole(message: '查看统计信息需要管理员权限')]
    public function statistics(Request $request): Response
    {
        try {
            $statistics = $this->fieldService->getStatistics();
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $statistics
            ]);
            
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取统计信息失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 根据字段类型获取字段
     *
     * @param Request $request
     * @return Response
     */
    #[RequireRole(message: '查看字段需要管理员权限')]
    public function getByType(Request $request): Response
    {
        try {
            $type = $request->param('type');
            $onlyActive = (bool)$request->param('only_active', true);
            
            if (empty($type)) {
                throw new ValidateException('字段类型不能为空');
            }
            
            $fields = $this->fieldService->getByType($type, $onlyActive);
            
            // 转换Bean为数组
            $list = [];
            foreach ($fields as $field) {
                $list[] = $field->toArray();
            }
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $list
            ]);
            
        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取字段失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取字段使用统计
     *
     * @param Request $request
     * @return Response
     */
    #[RequireRole(message: '查看字段统计需要管理员权限')]
    public function usageStats(Request $request): Response
    {
        try {
            $id = (int)$request->param('id');
            if (!$id) {
                throw new ValidateException('字段ID不能为空');
            }
            
            $stats = $this->fieldService->getFieldUsageStats($id);
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $stats
            ]);
            
        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取字段使用统计失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
}
