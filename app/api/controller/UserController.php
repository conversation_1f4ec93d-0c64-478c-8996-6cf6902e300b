<?php
declare(strict_types=1);

namespace app\api\controller;

use app\common\service\AdminUserService;
use app\api\annotation\RequireRole;
use think\Request;
use think\Response;
use think\exception\ValidateException;

/**
 * 管理员用户管理控制器
 *
 * 需要超级管理员权限
 */
#[RequireRole('super_admin', '只有超级管理员可以管理用户')]
class UserController
{
    protected AdminUserService $userService;
    
    public function __construct()
    {
        $this->userService = new AdminUserService();
    }
    
    /**
     * 获取用户列表
     * 
     * @param Request $request
     * @return Response
     */
    public function index(Request $request): Response
    {
        try {
            $page = (int)$request->param('page', 1);
            $limit = (int)$request->param('limit', 15);
            
            // 构建查询条件
            $where = [];
            if ($request->has('username')) {
                $where['username'] = $request->param('username');
            }
            if ($request->has('email')) {
                $where['email'] = $request->param('email');
            }
            if ($request->has('role')) {
                $where['role'] = $request->param('role');
            }
            if ($request->has('status')) {
                $where['status'] = $request->param('status');
            }
            
            $result = $this->userService->getList($page, $limit, $where);
            
            // 转换Bean为数组
            $list = [];
            foreach ($result['list'] as $user) {
                $list[] = $user->toArray();
            }
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => [
                    'list' => $list,
                    'total' => $result['total'],
                    'page' => $result['page'],
                    'limit' => $result['limit'],
                    'pages' => $result['pages']
                ]
            ]);
            
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取用户列表失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 获取用户详情
     * 
     * @param Request $request
     * @return Response
     */
    public function read(Request $request): Response
    {
        try {
            $id = (int)$request->param('id');
            if (!$id) {
                throw new ValidateException('用户ID不能为空');
            }
            
            $user = $this->userService->getById($id);
            if (!$user) {
                return json([
                    'code' => 404,
                    'message' => '用户不存在',
                    'data' => null
                ], 404);
            }
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $user->toArray()
            ]);
            
        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取用户详情失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 创建用户
     * 
     * @param Request $request
     * @return Response
     */
    public function create(Request $request): Response
    {
        try {
            $data = $request->only([
                'username', 'password', 'email', 'real_name', 
                'avatar', 'role', 'status'
            ]);
            
            $user = $this->userService->create($data);
            
            return json([
                'code' => 200,
                'message' => '创建成功',
                'data' => $user->toArray()
            ]);
            
        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '创建用户失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 更新用户
     * 
     * @param Request $request
     * @return Response
     */
    public function update(Request $request): Response
    {
        try {
            $id = (int)$request->param('id');
            if (!$id) {
                throw new ValidateException('用户ID不能为空');
            }
            
            $data = $request->only([
                'username', 'password', 'email', 'real_name', 
                'avatar', 'role', 'status'
            ]);
            
            $user = $this->userService->update($id, $data);
            
            return json([
                'code' => 200,
                'message' => '更新成功',
                'data' => $user->toArray()
            ]);
            
        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '更新用户失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 删除用户
     * 
     * @param Request $request
     * @return Response
     */
    public function delete(Request $request): Response
    {
        try {
            $id = (int)$request->param('id');
            if (!$id) {
                throw new ValidateException('用户ID不能为空');
            }
            
            $result = $this->userService->delete($id);
            
            return json([
                'code' => 200,
                'message' => $result ? '删除成功' : '删除失败',
                'data' => null
            ]);
            
        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '删除用户失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 修改密码
     * 
     * @param Request $request
     * @return Response
     */
    public function changePassword(Request $request): Response
    {
        try {
            $id = (int)$request->param('id');
            $oldPassword = $request->param('old_password', '');
            $newPassword = $request->param('new_password', '');
            
            if (!$id) {
                throw new ValidateException('用户ID不能为空');
            }
            
            if (empty($oldPassword) || empty($newPassword)) {
                throw new ValidateException('原密码和新密码不能为空');
            }
            
            $result = $this->userService->changePassword($id, $oldPassword, $newPassword);
            
            return json([
                'code' => 200,
                'message' => $result ? '密码修改成功' : '密码修改失败',
                'data' => null
            ]);
            
        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '修改密码失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
}
