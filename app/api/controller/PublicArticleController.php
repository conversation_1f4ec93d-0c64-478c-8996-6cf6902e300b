<?php
declare(strict_types=1);

namespace app\api\controller;

use think\Response;
use think\facade\Db;

/**
 * 公开文章API控制器 (无注解依赖)
 */
class PublicArticleController
{
    /**
     * 获取文章列表
     */
    public function index(): Response
    {
        try {
            $page = (int)(request()->get('page', 1));
            $limit = (int)(request()->get('limit', 10));
            $categoryId = request()->get('category_id');
            
            // 构建查询条件
            $where = ['status' => 'published']; // 只获取已发布的文章
            if ($categoryId) {
                $where['category_id'] = (int)$categoryId;
            }

            // 计算偏移量
            $offset = ($page - 1) * $limit;

            // 查询文章列表
            $articles = Db::table('articles')
                ->where($where)
                ->order('create_time', 'desc')
                ->limit($offset, $limit)
                ->select()
                ->toArray();

            // 查询总数
            $total = Db::table('articles')->where($where)->count();
            
            return json([
                'status' => 'success',
                'message' => '获取文章列表成功',
                'data' => [
                    'articles' => $articles,
                    'pagination' => [
                        'current_page' => $page,
                        'per_page' => $limit,
                        'total' => $total,
                        'last_page' => ceil($total / $limit)
                    ],
                    'timestamp' => date('Y-m-d H:i:s')
                ]
            ]);
        } catch (\Exception $e) {
            return json([
                'status' => 'error',
                'message' => '获取文章列表失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取单篇文章
     */
    public function read($id): Response
    {
        try {
            $article = Db::table('articles')
                ->where(['id' => (int)$id, 'status' => 'published'])
                ->find();
            
            if (!$article) {
                return json([
                    'status' => 'error',
                    'message' => '文章不存在',
                    'data' => null
                ], 404);
            }
            
            return json([
                'status' => 'success',
                'message' => '获取文章成功',
                'data' => $article
            ]);
        } catch (\Exception $e) {
            return json([
                'status' => 'error',
                'message' => '获取文章失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取热门文章
     */
    public function hot(): Response
    {
        try {
            $limit = (int)(request()->get('limit', 10));
            
            $articles = Db::table('articles')
                ->where('status', 'published')
                ->order('view_count', 'desc')
                ->order('create_time', 'desc')
                ->limit($limit)
                ->select()
                ->toArray();
            
            return json([
                'status' => 'success',
                'message' => '获取热门文章成功',
                'data' => [
                    'articles' => $articles,
                    'total' => count($articles),
                    'timestamp' => date('Y-m-d H:i:s')
                ]
            ]);
        } catch (\Exception $e) {
            return json([
                'status' => 'error',
                'message' => '获取热门文章失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取最新文章
     */
    public function latest(): Response
    {
        try {
            $limit = (int)(request()->get('limit', 12));
            
            $articles = Db::table('articles')
                ->where('status', 'published')
                ->order('create_time', 'desc')
                ->limit($limit)
                ->select()
                ->toArray();
            
            return json([
                'status' => 'success',
                'message' => '获取最新文章成功',
                'data' => [
                    'articles' => $articles,
                    'total' => count($articles),
                    'timestamp' => date('Y-m-d H:i:s')
                ]
            ]);
        } catch (\Exception $e) {
            return json([
                'status' => 'error',
                'message' => '获取最新文章失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取轮播文章
     */
    public function banner(): Response
    {
        try {
            $limit = (int)(request()->get('limit', 5));
            
            $articles = Db::table('articles')
                ->where(['status' => 'published', 'is_top' => 1])
                ->order('sort_order', 'asc')
                ->order('create_time', 'desc')
                ->limit($limit)
                ->select()
                ->toArray();
            
            return json([
                'status' => 'success',
                'message' => '获取轮播文章成功',
                'data' => [
                    'articles' => $articles,
                    'total' => count($articles),
                    'timestamp' => date('Y-m-d H:i:s')
                ]
            ]);
        } catch (\Exception $e) {
            return json([
                'status' => 'error',
                'message' => '获取轮播文章失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 搜索文章
     */
    public function search(): Response
    {
        try {
            $keyword = request()->get('keyword', '');
            $page = (int)(request()->get('page', 1));
            $limit = (int)(request()->get('limit', 10));
            
            if (empty($keyword)) {
                return json([
                    'status' => 'error',
                    'message' => '搜索关键词不能为空'
                ], 400);
            }
            
            $offset = ($page - 1) * $limit;
            
            $articles = Db::table('articles')
                ->where('status', 'published')
                ->where('title', 'like', "%{$keyword}%")
                ->order('create_time', 'desc')
                ->limit($offset, $limit)
                ->select()
                ->toArray();

            $total = Db::table('articles')
                ->where('status', 'published')
                ->where('title', 'like', "%{$keyword}%")
                ->count();
            
            return json([
                'status' => 'success',
                'message' => '搜索文章成功',
                'data' => [
                    'keyword' => $keyword,
                    'articles' => $articles,
                    'pagination' => [
                        'current_page' => $page,
                        'per_page' => $limit,
                        'total' => $total,
                        'last_page' => ceil($total / $limit)
                    ],
                    'timestamp' => date('Y-m-d H:i:s')
                ]
            ]);
        } catch (\Exception $e) {
            return json([
                'status' => 'error',
                'message' => '搜索文章失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
