<?php
declare(strict_types=1);

namespace app\api\controller;

use think\Response;
use app\common\service\ArticleCategoryService;

/**
 * 分类管理API控制器
 */
class CategoryController
{
    protected ArticleCategoryService $categoryService;
    
    public function __construct()
    {
        $this->categoryService = new ArticleCategoryService();
    }
    
    /**
     * 获取分类列表
     */
    public function index(): Response
    {
        try {
            $categories = $this->categoryService->getCachedMenuTree(true);

            return json([
                'status' => 'success',
                'message' => '获取分类列表成功',
                'data' => [
                    'categories' => $categories,
                    'total' => count($categories),
                    'timestamp' => date('Y-m-d H:i:s')
                ]
            ]);
        } catch (\Exception $e) {
            return json([
                'status' => 'error',
                'message' => '获取分类列表失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取单个分类信息
     */
    public function read($id): Response
    {
        try {
            $category = $this->categoryService->getById((int)$id);

            if (!$category) {
                return json([
                    'status' => 'error',
                    'message' => '分类不存在',
                    'data' => null
                ], 404);
            }

            return json([
                'status' => 'success',
                'message' => '获取分类信息成功',
                'data' => $category
            ]);
        } catch (\Exception $e) {
            return json([
                'status' => 'error',
                'message' => '获取分类信息失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 创建分类
     */
    public function create(): Response
    {
        try {
            $data = request()->post();
            
            // 验证必填字段
            if (empty($data['name'])) {
                return json([
                    'status' => 'error',
                    'message' => '分类名称不能为空'
                ], 400);
            }
            
            $result = $this->categoryService->create($data);
            
            return json([
                'status' => 'success',
                'message' => '创建分类成功',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return json([
                'status' => 'error',
                'message' => '创建分类失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 更新分类
     */
    public function update($id): Response
    {
        try {
            $data = request()->put();
            
            $result = $this->categoryService->update((int)$id, $data);
            
            if (!$result) {
                return json([
                    'status' => 'error',
                    'message' => '分类不存在或更新失败'
                ], 404);
            }
            
            return json([
                'status' => 'success',
                'message' => '更新分类成功',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return json([
                'status' => 'error',
                'message' => '更新分类失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 删除分类
     */
    public function delete($id): Response
    {
        try {
            $result = $this->categoryService->delete((int)$id);
            
            if (!$result) {
                return json([
                    'status' => 'error',
                    'message' => '分类不存在或删除失败'
                ], 404);
            }
            
            return json([
                'status' => 'success',
                'message' => '删除分类成功'
            ]);
        } catch (\Exception $e) {
            return json([
                'status' => 'error',
                'message' => '删除分类失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
