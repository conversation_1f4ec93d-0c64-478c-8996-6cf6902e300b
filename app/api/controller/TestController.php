<?php
declare(strict_types=1);

namespace app\api\controller;

use think\Response;

/**
 * API测试控制器
 */
class TestController
{
    /**
     * 测试方法
     */
    public function index(): Response
    {
        return json([
            "status" => "success",
            "message" => "API测试成功！🎉",
            "data" => [
                "timestamp" => date("Y-m-d H:i:s"),
                "app" => "api",
                "controller" => "TestController",
                "method" => "index",
                "version" => "1.0.0",
                "server_info" => [
                    "php_version" => PHP_VERSION,
                    "thinkphp_version" => "6.x",
                    "multi_app_enabled" => true
                ]
            ]
        ]);
    }

    /**
     * 健康检查
     */
    public function health(): Response
    {
        return json([
            "status" => "healthy",
            "message" => "API服务正常运行",
            "timestamp" => date("Y-m-d H:i:s"),
            "server_time" => time(),
            "uptime" => "正常",
            "database" => "连接正常",
            "cache" => "正常"
        ]);
    }
}
