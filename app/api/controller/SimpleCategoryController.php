<?php
declare(strict_types=1);

namespace app\api\controller;

use think\Response;
use think\facade\Db;

/**
 * 简化分类API控制器 (无注解依赖)
 */
class SimpleCategoryController
{
    /**
     * 获取分类列表
     */
    public function index(): Response
    {
        try {
            $categories = Db::table('article_categories')
                ->where('is_show', 1)
                ->order('sort_order', 'asc')
                ->order('id', 'asc')
                ->select()
                ->toArray();
            
            return json([
                'status' => 'success',
                'message' => '获取分类列表成功',
                'data' => [
                    'categories' => $categories,
                    'total' => count($categories),
                    'timestamp' => date('Y-m-d H:i:s')
                ]
            ]);
        } catch (\Exception $e) {
            return json([
                'status' => 'error',
                'message' => '获取分类列表失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取单个分类信息
     */
    public function read($id): Response
    {
        try {
            $category = Db::table('article_categories')
                ->where(['id' => (int)$id, 'is_show' => 1])
                ->find();
            
            if (!$category) {
                return json([
                    'status' => 'error',
                    'message' => '分类不存在',
                    'data' => null
                ], 404);
            }
            
            return json([
                'status' => 'success',
                'message' => '获取分类信息成功',
                'data' => $category
            ]);
        } catch (\Exception $e) {
            return json([
                'status' => 'error',
                'message' => '获取分类信息失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取分类树形结构
     */
    public function tree(): Response
    {
        try {
            $categories = Db::table('article_categories')
                ->where('is_show', 1)
                ->order('sort_order', 'asc')
                ->order('id', 'asc')
                ->select()
                ->toArray();
            
            // 构建树形结构
            $tree = $this->buildTree($categories);
            
            return json([
                'status' => 'success',
                'message' => '获取分类树成功',
                'data' => [
                    'tree' => $tree,
                    'total' => count($categories),
                    'timestamp' => date('Y-m-d H:i:s')
                ]
            ]);
        } catch (\Exception $e) {
            return json([
                'status' => 'error',
                'message' => '获取分类树失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 构建树形结构
     */
    private function buildTree(array $categories, int $parentId = 0): array
    {
        $tree = [];
        
        foreach ($categories as $category) {
            if (($category['parent_id'] ?? 0) == $parentId) {
                $children = $this->buildTree($categories, $category['id']);
                if (!empty($children)) {
                    $category['children'] = $children;
                }
                $tree[] = $category;
            }
        }
        
        return $tree;
    }
}
