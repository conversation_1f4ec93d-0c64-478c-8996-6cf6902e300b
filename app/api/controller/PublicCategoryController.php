<?php
declare(strict_types=1);

namespace app\api\controller;

use app\common\service\ArticleCategoryService;
use app\api\annotation\RequireRole;
use think\Request;
use think\Response;

/**
 * 公开分类控制器
 * 
 * 演示不同权限级别的接口：
 * - 无注解：公开接口，无需登录
 * - RequireRole()：需要登录的管理员
 * - RequireRole(isSuper: true)：需要超级管理员
 */
class PublicCategoryController
{
    protected ArticleCategoryService $categoryService;
    
    public function __construct()
    {
        $this->categoryService = new ArticleCategoryService();
    }
    
    /**
     * 获取公开的菜单树（无需权限）
     * 
     * @param Request $request
     * @return Response
     */
    public function publicTree(Request $request): Response
    {
        try {
            // 只获取显示的分类
            $tree = $this->categoryService->getMenuTree(true);
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $tree
            ]);
            
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取菜单树失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 获取公开的分类列表（无需权限）
     * 
     * @param Request $request
     * @return Response
     */
    public function publicList(Request $request): Response
    {
        try {
            $page = (int)$request->param('page', 1);
            $limit = (int)$request->param('limit', 10);
            
            // 只查询显示的分类
            $where = ['is_show' => 1];
            if ($request->has('type')) {
                $where['type'] = $request->param('type');
            }
            
            $result = $this->categoryService->getList($page, $limit, $where);
            
            // 转换Bean为数组，只返回必要字段
            $list = [];
            foreach ($result['list'] as $category) {
                $list[] = [
                    'id' => $category->id,
                    'parent_id' => $category->parentId,
                    'name' => $category->name,
                    'slug' => $category->slug,
                    'type' => $category->type,
                    'description' => $category->description,
                    'link_url' => $category->linkUrl,
                    'level' => $category->level,
                    'sort_order' => $category->sortOrder
                ];
            }
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => [
                    'list' => $list,
                    'total' => $result['total'],
                    'page' => $result['page'],
                    'limit' => $result['limit'],
                    'pages' => $result['pages']
                ]
            ]);
            
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取分类列表失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 获取管理员可见的分类统计（需要登录）
     * 
     * @param Request $request
     * @return Response
     */
    #[RequireRole(message: '查看分类统计需要管理员权限')]
    public function adminStats(Request $request): Response
    {
        try {
            $result = $this->categoryService->getList(1, 1);
            $total = $result['total'];
            
            $listCount = count($this->categoryService->getByType('list'));
            $singleCount = count($this->categoryService->getByType('single'));
            $linkCount = count($this->categoryService->getByType('link'));
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => [
                    'total' => $total,
                    'type_stats' => [
                        'list' => $listCount,
                        'single' => $singleCount,
                        'link' => $linkCount
                    ]
                ]
            ]);
            
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取统计失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 系统管理功能（需要超级管理员权限）
     * 
     * @param Request $request
     * @return Response
     */
    #[RequireRole(isSuper: true, message: '系统管理功能需要超级管理员权限')]
    public function systemManage(Request $request): Response
    {
        try {
            $action = $request->param('action');
            
            switch ($action) {
                case 'clear_cache':
                    // 模拟清理缓存
                    return json([
                        'code' => 200,
                        'message' => '缓存清理成功',
                        'data' => null
                    ]);
                    
                case 'rebuild_tree':
                    // 模拟重建分类树
                    return json([
                        'code' => 200,
                        'message' => '分类树重建成功',
                        'data' => null
                    ]);
                    
                default:
                    return json([
                        'code' => 400,
                        'message' => '无效的操作',
                        'data' => null
                    ], 400);
            }
            
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '系统管理操作失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
}
