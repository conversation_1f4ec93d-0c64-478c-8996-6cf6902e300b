<?php
declare(strict_types=1);

namespace app\api\controller;

use think\Response;
use app\common\repository\ArticleRepository;

/**
 * 简化文章管理API控制器 (无权限限制)
 */
class SimpleArticleController
{
    protected ArticleRepository $articleRepository;

    public function __construct()
    {
        $this->articleRepository = new ArticleRepository();
    }
    
    /**
     * 获取文章列表
     */
    public function index(): Response
    {
        try {
            $page = (int)(request()->get('page', 1));
            $limit = (int)(request()->get('limit', 10));
            $categoryId = request()->get('category_id');
            
            // 构建查询条件
            $where = [];
            if ($categoryId) {
                $where['category_id'] = (int)$categoryId;
            }
            
            $articles = $this->articleRepository->findPublished($page, $limit, $where);
            
            return json([
                'status' => 'success',
                'message' => '获取文章列表成功',
                'data' => [
                    'articles' => $articles['data'],
                    'pagination' => [
                        'current_page' => $page,
                        'per_page' => $limit,
                        'total' => $articles['total'],
                        'last_page' => ceil($articles['total'] / $limit)
                    ],
                    'timestamp' => date('Y-m-d H:i:s')
                ]
            ]);
        } catch (\Exception $e) {
            return json([
                'status' => 'error',
                'message' => '获取文章列表失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取单篇文章
     */
    public function read($id): Response
    {
        try {
            $article = $this->articleRepository->findById((int)$id);
            
            if (!$article) {
                return json([
                    'status' => 'error',
                    'message' => '文章不存在',
                    'data' => null
                ], 404);
            }
            
            return json([
                'status' => 'success',
                'message' => '获取文章成功',
                'data' => $article
            ]);
        } catch (\Exception $e) {
            return json([
                'status' => 'error',
                'message' => '获取文章失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取热门文章
     */
    public function hot(): Response
    {
        try {
            $limit = (int)(request()->get('limit', 10));
            $articles = $this->articleRepository->getHotArticles($limit);
            
            return json([
                'status' => 'success',
                'message' => '获取热门文章成功',
                'data' => [
                    'articles' => $articles,
                    'total' => count($articles),
                    'timestamp' => date('Y-m-d H:i:s')
                ]
            ]);
        } catch (\Exception $e) {
            return json([
                'status' => 'error',
                'message' => '获取热门文章失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取最新文章
     */
    public function latest(): Response
    {
        try {
            $limit = (int)(request()->get('limit', 12));
            $articles = $this->articleRepository->getLatestArticles($limit);
            
            return json([
                'status' => 'success',
                'message' => '获取最新文章成功',
                'data' => [
                    'articles' => $articles,
                    'total' => count($articles),
                    'timestamp' => date('Y-m-d H:i:s')
                ]
            ]);
        } catch (\Exception $e) {
            return json([
                'status' => 'error',
                'message' => '获取最新文章失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取轮播文章
     */
    public function banner(): Response
    {
        try {
            $limit = (int)(request()->get('limit', 5));
            $articles = $this->articleRepository->getBannerArticles($limit);
            
            return json([
                'status' => 'success',
                'message' => '获取轮播文章成功',
                'data' => [
                    'articles' => $articles,
                    'total' => count($articles),
                    'timestamp' => date('Y-m-d H:i:s')
                ]
            ]);
        } catch (\Exception $e) {
            return json([
                'status' => 'error',
                'message' => '获取轮播文章失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
