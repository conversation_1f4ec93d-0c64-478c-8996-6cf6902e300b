<?php

namespace orm;


use think\facade\Db;
use think\db\Query;
use think\Exception;
use think\exception\PDOException;
use think\Paginator;

/**
 * Created by lj.
 * User: lj
 * Date: 2021/6/29
 * Time: 3:47 下午
 */


/**
 * Class Db
 * @package think
 * @method Query table(string $table) static 指定数据表（含前缀）
 * @method Query name(string $name) static 指定数据表（不含前缀）
 * @method Query where(mixed $field, string $op = null, mixed $condition = null) static 查询条件
 * @method Query join(mixed $join, mixed $condition = null, string $type = 'INNER') static JOIN查询
 * @method Query union(mixed $union, boolean $all = false) static UNION查询
 * @method Query limit(mixed $offset, integer $length = null) static 查询LIMIT
 * @method Query order(mixed $field, string $order = null) static 查询ORDER
 * @method Query cache(mixed $key = null , integer $expire = null) static 设置查询缓存
 * @method mixed value(string $field) static 获取某个字段的值
 * @method array column(string $field, string $key = '') static 获取某个列的值
 * @method Query view(mixed $join, mixed $field = null, mixed $on = null, string $type = 'INNER') static 视图查询
 * @method mixed find(mixed $data = null) static 查询单个记录
 * @method mixed select(mixed $data = null) static 查询多个记录
 * @method integer insert(array $data, boolean $replace = false, boolean $getLastInsID = false, string $sequence = null) static 插入一条记录
 * @method integer insertGetId(array $data, boolean $replace = false, string $sequence = null) static 插入一条记录并返回自增ID
 * @method integer insertAll(array $dataSet) static 插入多条记录
 * @method integer update(array $data) static 更新记录
 * @method integer delete(mixed $data = null) static 删除记录
 * @method boolean chunk(integer $count, callable $callback, string $column = null) static 分块获取数据
 * @method mixed query(string $sql, array $bind = [], boolean $master = false, bool $pdo = false) static SQL查询
 * @method integer execute(string $sql, array $bind = [], boolean $fetch = false, boolean $getLastInsID = false, string $sequence = null) static SQL执行
 * @method Paginator paginate(integer $listRows = 15, mixed $simple = null, array $config = []) static 分页查询
 * @method mixed transaction(callable $callback) static 执行数据库事务
 * @method void startTrans() static 启动事务
 * @method void commit() static 用于非自动提交状态下面的查询提交
 * @method void rollback() static 事务回滚
 * @method boolean batchQuery(array $sqlArray) static 批处理执行SQL语句
 * @method string quote(string $str) static SQL指令安全过滤
 * @method string getLastInsID($sequence = null) static 获取最近插入的ID
 */
class EntityManager
{
    public function from($bean): EntityManager
    {
        $this->metaInfo = Parser::parse($bean);
        $this->realQuery = Db::name($this->metaInfo->tableName);
        return $this;
    }

    public static function create($bean): EntityManager
    {
        $manger = new EntityManager();
        $manger = $manger->from($bean);
        return $manger;
    }

    /**
     * 调用驱动类的方法
     * @access public
     * @param  string $method 方法名
     * @param  array  $params 参数
     * @return mixed
     */
    public static function __callStatic($method, $params)
    {
        return call_user_func_array(['Db', $method], $params);
    }

    private ?TableMetaInfo $metaInfo = null;
    private ?Query $realQuery = null;
    public function __call($method, $args)
    {
        $result = call_user_func_array([$this->realQuery, $method], $args);
        if ($result instanceof Query) {
            return $this;
        }
        return $result;
    }

    public function findResult($bean = null)
    {
        $info = $this->realQuery->find();

        if ($bean) {
            return SqlTool::getBean(Parser::parse($bean), $info);
        } else {
            return SqlTool::getBean($this->metaInfo, $info);
        }
    }

    public function selectResult($bean = null)
    {
        $list = $this->realQuery->select();
        $beans = [];
        if ($bean) {
            $metaInfo = Parser::parse($bean);
            foreach ($list as $key => $value) {
                $beans[$key] = SqlTool::getBean($metaInfo, $value);
            }
        } else {
            foreach ($list as $key => $value) {
                $beans[$key] = SqlTool::getBean($this->metaInfo, $value);
            }
        }
        return $beans;
    }

    public function selectResultFilter(\Closure $call, $bean = null)
    {
        $list = $this->realQuery->select();
        $beans = [];
        if ($bean) {
            $metaInfo = Parser::parse($bean);
            foreach ($list as $key => $value) {
                $beans[$key] = $call(SqlTool::getBean($metaInfo, $value), $key);
            }
        } else {
            foreach ($list as $key => $value) {
                $beans[$key] = $call(SqlTool::getBean($this->metaInfo, $value), $key);
            }
        }
        return $beans;
    }

    /**
     * 批量插入记录
     * @access public
     * @param mixed     $dataSet 数据集
     * @param boolean   $replace  是否replace
     * @param integer   $limit   每次写入数据限制
     * @return integer|string
     */
    public function insertAllBean(array $beanSet, $replace = false, $limit = null)
    {
        $dataSet = [];
        foreach ($beanSet as $value) {
            $dataSet[] = SqlTool::getAddModel(Parser::parse($value), $value);
        }
        return $this->realQuery->insertAll($dataSet, $replace, $limit);
    }

    /**
     * 插入记录
     * @access public
     * @param mixed   $data         数据
     * @param boolean $replace      是否replace
     * @param boolean $getLastInsID 返回自增主键
     * @param string  $sequence     自增序列名
     * @return integer|string
     */
    public function insertBean($bean, $replace = false, $getLastInsID = false, $sequence = null)
    {
        $data = SqlTool::getAddModel(Parser::parse($bean), $bean);
        return $this->realQuery->insert($data, $replace, $getLastInsID, $sequence);
    }

    /**
     * 插入记录并获取自增ID
     * @access public
     * @param mixed   $data     数据
     * @param boolean $replace  是否replace
     * @param string  $sequence 自增序列名
     * @return integer|string
     */
    public function insertBeanGetId($bean, $replace = false, $sequence = null)
    {
        $data = SqlTool::getAddModel(Parser::parse($bean), $bean);
        return $this->realQuery->insertGetId($data, $replace, $sequence);
    }

    /**
     * 更新记录
     * @access public
     * @param mixed $data 数据
     * @return integer|string
     * @throws Exception
     * @throws PDOException
     */
    public function updateBean($bean)
    {
        $data = SqlTool::getUpdateModel(Parser::parse($bean), $bean);
        return $this->realQuery->update($data);
    }

    public function getMetaInfo()
    {
        return $this->metaInfo;
    }
}
